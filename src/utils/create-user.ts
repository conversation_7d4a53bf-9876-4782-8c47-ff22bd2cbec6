import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { insertRow, selectOneRow } from '@/lib/supabase/utils';
import { SupabaseClient } from '@supabase/supabase-js';

interface ClientEmailData {
  id: string | number;
  email: string;
  orgIdNum?: number | null;
}

export const createUser = async (
  clientData: any,
  validate: boolean = true,
  showToast: boolean = true,
  client?: SupabaseClient,
  error: string = 'Email already exists.',
  createEmail: boolean = true
) => {
  const { email, organization_id, ...clientPayload } = clientData;

  if (!email && createEmail) throw new Error('Email is not provided!');

  let orgIdNum: number | null = null;
  if (organization_id !== undefined && organization_id !== null) {
    orgIdNum = Number(organization_id);
    if (isNaN(orgIdNum)) {
      throw new Error('Invalid organization_id: must be a number');
    }
  }

  if (validate) {
    const existing = await checkIfEmailExist(
      email,
      'id, email',
      showToast,
      client
    );
    if (existing) throw new Error(error);
  }

  const newClient = await insertRow<any>(
    tableNames.clients,
    {
      ...clientPayload,
      ...(orgIdNum !== null ? { organization_id: orgIdNum } : {}),
    },
    client
  );

  if (createEmail && newClient.id) {
    console.log('proceed to create email', newClient.id);
    await createClientEmails(
      { id: newClient.id, email, orgIdNum: orgIdNum },
      client
    );
  }

  return newClient;
};

export const checkIfEmailExist = async <T = any>(
  email: string,
  select: string = '*',
  showToast: boolean = true,
  client?: SupabaseClient,
  error: string = 'Email already exists.'
): Promise<T | null> => {
  const record = await selectOneRow<T>(
    tableNames.client_emails,
    [{ column: 'email', operator: 'eq', value: email.toLowerCase() }],
    select,
    client
  );

  if (record && showToast) {
    toaster.create({
      description: error,
      type: 'error',
    });
  }

  return record;
};

export const createClientEmails = async (
  emailData: ClientEmailData,
  client?: SupabaseClient
) => {
  const { id, email, orgIdNum } = emailData;
  console.log('inside create email function', emailData);

  const record = await insertRow(
    tableNames.client_emails,
    {
      client_id: id,
      email,
      ...(orgIdNum !== null && orgIdNum !== undefined
        ? { organization_id: orgIdNum }
        : {}),
      is_primary_email: true,
    },
    client
  );

  return record;
};

import moment from 'moment';

export const getPrimaryEmail = (
  clientEmails: Array<any> | any
): string | null => {
  // Ensure clientEmails is always an array
  const emailArray = Array.isArray(clientEmails) ? clientEmails : [];

  if (emailArray.length === 0) {
    return null; // Return null if the array is empty
  }

  // Find the primary email
  const primaryEmail = emailArray.find((email: any) => email?.is_primary_email);
  // Return the email if found, otherwise return null
  return primaryEmail ? primaryEmail.email : null;
};

const changeProvinceShortForm = (province: any) => {
  if (!province) {
    return 'NULL';
  }

  const provinceMappings = [
    { names: ['Alberta', 'AB'], shortForm: 'AB' },
    { names: ['Nova Scotia'], shortForm: 'NS' },
    { names: ['Manitoba'], shortForm: 'MB' },
    { names: ['Saskatchewan'], shortForm: 'SK' },
    { names: ['Newfoundland and Labrador'], shortForm: 'NL' },
    { names: ['Prince Edward Island'], shortForm: 'PE' },
    {
      names: ['British Columbia', 'British Columbia\nCanada', 'BC'],
      shortForm: 'BC',
    },
    { names: ['Ontario', 'ON'], shortForm: 'ON' },
    { names: ['Quebec', 'QC'], shortForm: 'QC' },
    { names: ['Other - Canada'], shortForm: 'Other - Canada' },
    { names: ['Other - USA'], shortForm: 'Other - USA' },
    { names: ['New Brunswick'], shortForm: 'NB' },
    { names: ['Other'], shortForm: 'Other' },
  ];

  // Find the matching province mapping
  for (const mapping of provinceMappings) {
    if (mapping.names.includes(province)) {
      return mapping.shortForm;
    }
  }

  // If no match is found, return the original province
  return province;
};

function convertToNumber(str: any) {
  // Try converting the string to a number
  const num = Number(str);

  // Check if the conversion resulted in NaN (not a number)
  if (!isNaN(num)) {
    // If it's a valid number, return it
    return num;
  } else {
    // If it's not a valid number, return the first character as a number
    return parseInt(str[0]);
  }
}

const getDateDisplay = (date?: Date | string | null) => {
  const effectiveDate =
    date && moment(date).isValid() ? moment(date) : moment();
  const selectDateFormatted = effectiveDate.format('YYYY-MM-DD');
  const today = moment().format('YYYY-MM-DD');
  const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
  const tomorrow = moment().add(1, 'day').format('YYYY-MM-DD');

  if (selectDateFormatted === today) return 'Today';
  if (selectDateFormatted === yesterday) return 'Yesterday';
  if (selectDateFormatted === tomorrow) return 'Tomorrow';

  // Custom formatted fallback, e.g., "Fri, 25th May, 2025"
  return effectiveDate.format('ddd, Do MMM, YYYY');
};

export { changeProvinceShortForm, convertToNumber, getDateDisplay };

export function extractSessionDetails(input: string): {
  session_duration: number;
  session_quantity: number;
} {
  // Regex to match "<number>h<x number>"
  //console.log('input is ', input);
  if (!input) {
    return { session_duration: 0, session_quantity: 0 };
  }

  const regex = /(\d*\.?\d+)\s*(?:h|hr|hour)s?\s*[xX]\s*(\d+)/i;

  const match = input.match(regex);
  if (!match) {
    return { session_duration: 0, session_quantity: 0 };

    // throw new Error('Invalid format. Could not extract session details.');
  }
  //console.log('match is ', match);

  // Extract hours and quantity
  const hours = parseFloat(match[1]);
  const quantity = parseInt(match[2], 10);

  // Calculate session duration in minutes
  const session_duration = hours * 60;

  return {
    session_duration,
    session_quantity: quantity,
  };
}

// Helper function to extract core name variations
export function generateNameVariations(
  displayName: string,
  firstName: string,
  lastName: string
) {
  const variations = new Set<string>();

  function addWithNormalized(name?: string) {
    if (!name) return;
    const trimmed = name.trim();
    if (!trimmed) return;

    // Add original
    variations.add(trimmed);

    // Add normalized (accent-free) version
    variations.add(
      trimmed
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .trim()
    );
  }

  // Add display name
  addWithNormalized(displayName);

  // Add first + last
  if (firstName && lastName) {
    addWithNormalized(`${firstName} ${lastName}`);
  }

  // Add parentheses match
  const parenthesesMatch = displayName?.match(/\(([^)]+)\)/);
  if (parenthesesMatch) {
    const nameInParentheses = parenthesesMatch[1].trim();
    addWithNormalized(nameInParentheses);
    if (lastName) addWithNormalized(`${nameInParentheses} ${lastName}`);
  }

  // Add standalone first name
  addWithNormalized(firstName);

  return Array.from(variations).filter((name) => name.length > 0);
}

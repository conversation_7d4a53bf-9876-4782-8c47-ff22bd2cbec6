import { SupabaseClient } from '@supabase/supabase-js';
import supabase from './client';

type Filter = {
  column: string;
  operator:
    | 'eq'
    | 'neq'
    | 'gt'
    | 'gte'
    | 'lt'
    | 'lte'
    | 'like'
    | 'ilike'
    | 'is'
    | 'in'
    | 'contains';
  value: any;
};

const operatorMap = {
  eq: (q: any, col: string, val: any) => q.eq(col, val),
  neq: (q: any, col: string, val: any) => q.neq(col, val),
  gt: (q: any, col: string, val: any) => q.gt(col, val),
  gte: (q: any, col: string, val: any) => q.gte(col, val),
  lt: (q: any, col: string, val: any) => q.lt(col, val),
  lte: (q: any, col: string, val: any) => q.lte(col, val),
  like: (q: any, col: string, val: any) => q.like(col, val),
  ilike: (q: any, col: string, val: any) => q.ilike(col, val),
  is: (q: any, col: string, val: any) => q.is(col, val),
  in: (q: any, col: string, val: any) => q.in(col, val),
  contains: (q: any, col: string, val: any) => q.contains(col, val),
};

export async function insertRow<T>(
  table: string,
  payload: Partial<T>,
  client?: SupabaseClient
): Promise<T> {
  const sb = client ?? supabase;
  const { data, error } = await sb.from(table).insert(payload).select();
  console.log('insertRow result:', { table, payload, data, error });

  if (error) throw error;

  const record = data?.[0];
  if (!record) throw new Error(`Insert into ${table} failed: no data returned`);

  return record as T;
}

export async function selectRows<T>(
  table: string,
  filters?: Filter[],
  columns: string = '*',
  client?: SupabaseClient
): Promise<T[]> {
  const sb = client ?? supabase;
  let query = sb.from(table).select(columns);

  if (filters && filters.length > 0) {
    for (const { column, operator, value } of filters) {
      const apply = operatorMap[operator];
      if (apply) query = apply(query, column, value);
    }
  }

  const { data, error } = await query;

  if (error) throw error;

  return (data ?? []) as T[];
}

export async function selectOneRow<T>(
  table: string,
  filters?: Filter[],
  columns: string = '*',
  client?: SupabaseClient
): Promise<T | null> {
  const rows = await selectRows<T>(table, filters, columns, client);
  return rows.length > 0 ? rows[0] : null;
}

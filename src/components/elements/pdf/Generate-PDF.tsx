'use client';
import { pdf } from '@react-pdf/renderer';
import { PDFGenerator, TPDF } from './PDF-Generator';

export const generateAndDownloadPDF = async ({
  name,
  receiptNumber,
  date,
  activity,
  quantity,
  referral,
  rate,
  balance,
  memo,
  email,
  dueDate,
  amountDue,
  resolvedAmountDue,
  transactions,
  invoice,
}: TPDF) => {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  try {
    // Generate the PDF
    const blob = await pdf(
      <PDFGenerator
        name={name}
        email={email}
        dueDate={dueDate}
        amountDue={amountDue}
        resolvedAmountDue={resolvedAmountDue}
        transactions={transactions}
        receiptNumber={receiptNumber}
        referral={referral}
        date={date}
        activity={activity}
        quantity={quantity}
        rate={rate}
        balance={balance}
        memo={memo}
        invoice={invoice}
      />
    ).toBlob();

    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download =
      `${org?.name + '-' || ''}invoice-Receipt-${receiptNumber}.pdf`.replace(
        /-null/g,
        ''
      );

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  } catch (error) {
    throw new Error('Failed to generate PDF');
  }
};

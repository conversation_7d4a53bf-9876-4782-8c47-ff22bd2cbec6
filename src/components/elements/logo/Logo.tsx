'use client';

import soapLogo from '@/assets/soapLogo.png';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { getCookie } from '@/utils/cookie-helper';
import { Box, Image } from '@chakra-ui/react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

type LogoProps = {
  to?: string;
  src?: string;
  isPublic?: boolean;
};

export default function Logo({ to, src, isPublic = false }: LogoProps) {
  const { UserFromQuery } = useSupabaseSession();
  // Initialize with null to match server rendering
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const cookie = getCookie('user_data');
      const cookieLogo = cookie
        ? JSON.parse(cookie)?.organization?.logo_url
        : null;
      const userLogo = UserFromQuery?.organization?.logo_url;

      if (userLogo && !isPublic) {
        setLogoUrl(userLogo);
      } else if (cookieLogo && !isPublic) {
        setLogoUrl(cookieLogo);
      } else if (src && isPublic) {
        setLogoUrl(src);
      } else {
        setLogoUrl(soapLogo.src);
      }
    }
  }, [UserFromQuery?.organization?.logo_url, src, isPublic]);

  // Render the logo with fixed dimensions
  const LogoImage = (
    <Box
      w="12rem"
      h="6rem"
      padding="1rem"
      display="flex"
      justifyContent="center"
      alignItems="center"
    >
      {logoUrl && (
        <Image
          src={logoUrl}
          alt="logo"
          w="100%"
          h="100%"
          borderRadius="md"
          onError={() => setLogoUrl(soapLogo.src)}
          objectFit={'contain'}
        />
      )}
    </Box>
  );

  return to ? <Link href={to}>{LogoImage}</Link> : LogoImage;
}

'use client';
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import PageLoader from '@/components/elements/loader/PageLoader';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

export default function ClientLayout({
  children,
  organization_name,
}: {
  children: React.ReactNode;
  organization_name: string;
}) {
  const pathName = usePathname();
  const isFree =
    pathName.includes('token-verification') || pathName.includes('check-email');

  const { session, user, UserFromQueryLoading } = useSupabaseSession();

  const { data: OrganizationData, isLoading: OrganizationLoading } =
    useGetOrganizationBySlugQuery({
      slug: organization_name,
      isPublic: 'true',
    });

  const organization = OrganizationData?.[0];
  const router = useRouter();

  // Don't make auth decisions while user data is still loading
  const isUserLoading = UserFromQueryLoading;
  const hasValidSession = session && user?.email;

  useEffect(() => {
    // Wait for both organization and user data to finish loading
    if (!OrganizationLoading && !isUserLoading && organization) {
      // Only redirect if we're sure there's no valid session and it's not a free route
      if (!hasValidSession && !isFree) {
        console.log('Redirecting to login - no valid session');
        router.replace(`/client/${organization_name}/login`);
        return;
      }
    }

    // Handle organization not found
    if (!OrganizationLoading && !organization) {
      router.replace('/client/not-found');
    }
  }, [
    OrganizationLoading,
    isUserLoading,
    organization,
    hasValidSession,
    isFree,
    organization_name,
    router,
    pathName,
  ]);

  // Show loader while either organization or user data is loading (except for free routes)
  if ((OrganizationLoading || isUserLoading) && !isFree) {
    return <PageLoader />;
  }

  // Only render children if we have organization data
  if (organization) {
    return <>{children}</>;
  }

  // Show loader as fallback
  return <PageLoader />;
}

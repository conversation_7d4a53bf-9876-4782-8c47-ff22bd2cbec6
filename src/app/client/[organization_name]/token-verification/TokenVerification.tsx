'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
// import { validateClientSession } from '../../_util/validate-session';
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import PageLoader from '@/components/elements/loader/PageLoader';
import { createSupabaseClient } from '@/lib/supabase/client';

export default function TokenVerification({
  organization_name,
}: {
  organization_name: string;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  // const [loading, setLoading] = useState(true);
  const supabse = createSupabaseClient();
  const token = searchParams.get('token');
  const hash = typeof window !== 'undefined' ? window.location.hash : '';
  const params = new URLSearchParams(hash.substring(1)); // remove #
  const access_token = params.get('access_token');
  const refresh_token = params.get('refresh_token');
  console.log('access_token is ', access_token);
  console.log('refresh_token is ', refresh_token);
  console.log('hash is ', hash);

  const { data: OrganizationData, isLoading: OrganizationLoading } =
    useGetOrganizationBySlugQuery({
      slug: organization_name,
      isPublic: 'true',
    });
  const organization = OrganizationData?.[0];

  console.log('token is ', token);
  console.log('organization_name is ', organization_name);

  useEffect(() => {
    const init = async () => {
      if (!OrganizationLoading && organization) {
        const session = await supabse.auth.setSession({
          access_token: String(access_token),
          refresh_token: String(refresh_token),
        });
        //console.log('session is ', session);

        if (!session || !session?.data?.user?.email) {
          console.log('a reditece us happening', session);
          router.replace(`/client/${organization_name}/login`);
        } else {
          router.replace(`/client/${organization_name}/me/invoices`);
        }
      }
    };
    if (!OrganizationLoading && !organization) {
      router.replace('/client/not-found');
    }
    init();
  }, [
    OrganizationLoading,
    organization,
    organization_name,
    router,
    access_token,
    refresh_token,
    supabse.auth,
  ]);

  return (
    <div>
      <PageLoader />
    </div>
  );
}

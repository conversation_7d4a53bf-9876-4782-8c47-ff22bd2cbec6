'use client';

import { useGetClientBookingsQueryCM } from '@/api/clients/module/get-client-bookings';
import { useGetClientByEmailQueryCM } from '@/api/clients/module/get-client-by-email';
// import { useGetClientInvoicesQueryCM } from '@/api/clients/module/get-client-invoices';
// import { useGetInvoicesByClientQuery } from '@/api/invoices/get-invoices-by-client-id';
// import { useGetUserBySlugQuery } from '@/api/users/find-by-event-slug';
import { useGetAllInvoicesQuery } from '@/api/newsf/queries';
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import EmptyState from '@/components/elements/EmptyState';
import PageLoader from '@/components/elements/loader/PageLoader';
import CustomTable from '@/components/table/CustomTable';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { IGetInvoicesFilterState } from '@/store/filters/invoices';
import { Box, Tabs } from '@chakra-ui/react';
import { getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table';
import { useRecoilState } from 'recoil';
import { columnDef } from '../bookings/column-def';
import { useGetNewSfClientInvoicesQueryCM } from '../profile/query';
import { createColumnDef } from './column-def';

export default function Invoices({
  organization_name,
}: {
  organization_name: string;
}) {
  const session = useSupabaseSession();
  const [filter, setFilter] = useRecoilState(IGetInvoicesFilterState);

  console.log('session is ', session);
  const { data: OrganizationData, isLoading: OrganizationLoading } =
    useGetOrganizationBySlugQuery({
      slug: organization_name,
      isPublic: 'true',
    });

  // console.log('OrganizationData', OrganizationData);
  const { data: ClientData, isLoading: ClientLoading } =
    useGetClientByEmailQueryCM(
      {
        email: String(session?.user?.email),
        organization_id: OrganizationData?.[0]?.id,
      },
      {
        enabled:
          Boolean(OrganizationData?.[0]?.id) &&
          Boolean(String(session?.user?.email)),
      }
    );
  const { data: InvoicesData, isLoading: InvoicesLoading } =
    useGetAllInvoicesQuery(
      {
        client_id: ClientData?.client_id,
        organization_id: OrganizationData?.[0]?.id,
      },
      {
        enabled:
          Boolean(ClientData?.client_id) &&
          Boolean(String(session?.user?.email)),
      }
    );
  const { data: NewSfInvoicesData, isLoading: NewSfInvoicesLoading } =
    useGetNewSfClientInvoicesQueryCM(
      {
        id: ClientData?.client_id,
        organization_id: OrganizationData?.[0]?.id,
      },
      {
        enabled:
          Boolean(ClientData?.client_id) &&
          Boolean(String(session?.user?.email)),
      }
    );

  // console.log('NewSfInvoicesData', NewSfInvoicesData?.data);
  const { data: BookingsData, isLoading: BookingsLoading } =
    useGetClientBookingsQueryCM(
      {
        id: ClientData?.client_id,
        organization_id: OrganizationData?.[0]?.id,
      },
      {
        enabled:
          Boolean(ClientData?.client_id) &&
          Boolean(String(session?.user?.email)),
      }
    );

  const sortedInvoices = (InvoicesData ?? []).slice().sort((a: any, b: any) => {
    const dateDiff =
      new Date(b.invoice_date).getTime() - new Date(a.invoice_date).getTime();

    // If the invoice_date is the same, sort by invoice_number
    if (dateDiff === 0) {
      return parseInt(b.invoice_number, 10) - parseInt(a.invoice_number, 10);
    }

    return dateDiff;
  });

  // console.log('BookingsData is ', BookingsData);

  if (
    OrganizationLoading ||
    InvoicesLoading ||
    ClientLoading ||
    BookingsLoading ||
    NewSfInvoicesLoading
  ) {
    return <PageLoader />;
  }

  // console.log('InvoicesData is ', InvoicesData);

  return (
    <Box>
      {/* <Heading
        mb={{ base: '2', md: '4' }}
        fontSize={{ base: '1.3rem', md: '1.5rem' }}
      >
        
      </Heading> */}

      <Tabs.Root defaultValue="invoices" lazyMount>
        <Box
          position={'sticky'}
          zIndex={'10'}
          bg={'white'}
          borderBottom={{ lg: '1px solid' }}
          borderColor={{ lg: 'gray.50' }}
          overflow={'hidden'}
          top={'-10'}
        >
          <Tabs.List
            display={{ base: 'none', lg: 'flex' }}
            border={'none'}
            alignItems={'center'}
            //gap={'6'}
            spaceX={'4'}
            overflowY={'hidden'}
            overflowX={'auto'}
            className="scroll-container"
          >
            <Tabs.Trigger
              value="invoices"
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Invoices
            </Tabs.Trigger>
            <Tabs.Trigger
              value="bookings"
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Bookings
            </Tabs.Trigger>
          </Tabs.List>
        </Box>

        <Box overflowY={'auto'} h={'full'}>
          <Tabs.Content value="invoices">
            <Box w={'full'} overflowX={'auto'}>
              {sortedInvoices && sortedInvoices?.length > 0 ? (
                <CustomTable
                  columnDef={createColumnDef(
                    organization_name,
                    OrganizationData
                      ? parseInt(OrganizationData?.[0]?.id)
                      : OrganizationData?.[0]?.id,
                    'public'
                    // data?.id,
                  )}
                  data={
                    OrganizationData?.[0]?.id === 1
                      ? sortedInvoices
                      : NewSfInvoicesData?.data || []
                  }
                  filter={{
                    tableName: 'Invoices',
                  }}
                  tableOptions={{
                    pageCount: 1,
                    enableRowSelection: true,
                  }}
                  total={
                    OrganizationData?.[0]?.id === 1
                      ? sortedInvoices?.length
                      : NewSfInvoicesData?.pagination?.total_count
                  }
                  enableSorting={true}
                  pagination={{
                    row: Number(filter?.size),
                    page: Number(filter?.currentPage),
                  }}
                  setPagination={{
                    onPageChange: (e) =>
                      setFilter({ ...filter, currentPage: e }),
                    onRowChange: (e) => setFilter({ ...filter, size: e }),
                  }}
                />
              ) : (
                <EmptyState text="No invoices" />
              )}
            </Box>
          </Tabs.Content>

          <Tabs.Content value="bookings">
            <Box w={'full'} overflowX={'auto'}>
              {BookingsData && BookingsData?.length > 0 ? (
                <CustomTable
                  columnDef={columnDef}
                  data={BookingsData || []}
                  filter={{
                    tableName: 'Bookings',
                  }}
                  total={BookingsData?.length}
                  tableOptions={{
                    pageCount: 1,
                    manualPagination: true,
                    getCoreRowModel: getCoreRowModel(),
                    getPaginationRowModel: getPaginationRowModel(),
                  }}
                />
              ) : (
                <EmptyState text="No bookings" />
              )}
            </Box>
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </Box>
  );
}

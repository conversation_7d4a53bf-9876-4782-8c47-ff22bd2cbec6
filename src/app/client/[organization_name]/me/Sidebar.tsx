import soapLogo from '@/assets/soapLogo.png';
import { Box, Flex, Separator, Stack, type StackProps } from '@chakra-ui/react';
import {
  LuUser, // LuBookmark,
} from 'react-icons/lu';

import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import Logo from '@/components/elements/logo/Logo';
import { Button, type ButtonProps } from '@chakra-ui/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LiaFileInvoiceSolid } from 'react-icons/lia';

interface Props extends ButtonProps {
  href?: string;
  onClose?: () => void;
}

const SidebarLink = (props: Props) => {
  const { children, href, ...buttonProps } = props;
  const pathName = usePathname();
  const isActive = pathName === href;
  // console.log({ pathName });
  // console.log({ href });

  return (
    <Button
      variant="ghost"
      width="full"
      justifyContent="start"
      gap="3"
      onClick={props.onClose}
      _hover={{
        bg: 'pink.50',
        color: '#e97a5b',
        transition: 'all .3s ease',
        fontWeight: 500,
      }}
      py={'6'}
      // _currentPage={{
      //   color: '#e97a5b',
      //   fontWeight: 500,
      // }}
      rounded={'md'}
      color={isActive ? '#e97a5b' : 'inherit'}
      fontWeight={isActive ? 500 : 400}
      fontSize={'1rem'}
      asChild
      {...buttonProps}
    >
      <Link href={String(href)}>{children}</Link>
    </Button>
  );
};

export const Sidebar = (
  props: StackProps & { organization_name: string; onClose?: () => void }
) => {
  //const { data: OrganizationData } =
  useGetOrganizationBySlugQuery({
    slug: props.organization_name,
    isPublic: 'true',
  });
  //console.log('OrganizationData is ', OrganizationData);

  return (
    <Flex
      direction="column"
      flex="1"
      // p={{ base: '4', md: '6' }}
      bg="white"
      borderRightWidth={{ base: '0px', lg: '1px' }}
      borderColor={'gray.50'}
      justifyContent="space-between"
      //maxW="xs"
      h="100vh"
      p={'0'}
      position="relative"
      {...props}
    >
      <Stack gap="0" pt={'0'}>
        <Box w={'full'} h={'5.5rem'}>
          <Logo src={soapLogo?.src} />
        </Box>
        <Separator mb={'1rem'} borderColor={'gray.50'} />

        <Stack px={'4'} gap="3">
          <SidebarLink
            href={`/client/${props.organization_name}/me/profile`}
            onClose={props.onClose}
          >
            <LuUser />
            Profile
          </SidebarLink>
          <SidebarLink
            href={`/client/${props.organization_name}/me/invoices`}
            onClose={props.onClose}
          >
            <LiaFileInvoiceSolid />
            Activity
          </SidebarLink>
        </Stack>
      </Stack>
      {/* <Stack gap="4" separator={<StackSeparator />}>
        <Box />
        <Stack gap="1">
          <SidebarLink>
            <LuCircleHelp /> Help Center
          </SidebarLink>
          <SidebarLink>
            <LuSettings /> Settings
          </SidebarLink>
        </Stack>
       </Stack> */}
    </Flex>
  );
};

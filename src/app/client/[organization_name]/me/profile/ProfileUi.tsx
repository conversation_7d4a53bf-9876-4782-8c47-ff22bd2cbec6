'use client';

import {
  Ava<PERSON>,
  Box,
  Container,
  Heading,
  HStack,
  SimpleGrid,
  Tabs,
  Text,
  VStack,
} from '@chakra-ui/react';
import { FiCreditCard, FiSettings, FiUser } from 'react-icons/fi';
import PaymentContent from './PaymentContent';
import ProfileContent from './ProfileContent';
import SettingContent from './SettingContent';

interface ClientData {
  id: number;
  email: string;
  organization_id: number;
  client_id: number;
  client: {
    id: number;
    first_name: string;
    last_name: string;
    phone?: string;
  };
}

export default function Component({
  clientData,
  refetch,
}: {
  clientData: ClientData | null;
  refetch: () => void;
}) {
  const fullName = `${clientData?.client?.first_name ?? ''} ${clientData?.client?.last_name ?? ''}`;
  return (
    <Box h={'full'} p={'0'} overflow={'auto'} className="no-scrollbar">
      <Container p={'0'} maxW="4xl">
        {/* Header */}
        <Box
          mb={8}
          border="1px solid"
          borderColor={'gray.50'}
          rounded={'md'}
          py={'6'}
          px={'5'}
        >
          <HStack gap={4}>
            <Avatar.Root
              size="2xl"
              fontWeight={'600'}
              bg={'orange.100'}
              color={'orange.600'}
            >
              <Avatar.Fallback name={fullName} />
            </Avatar.Root>
            <VStack align="start" gap={0}>
              <Heading fontSize={{ base: 'lg', lg: 'xl' }}>{fullName}</Heading>
              <Text color="#4b5563" fontSize={{ base: 'sm', lg: 'md' }}>
                {clientData?.email ?? ''}
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* Tabs */}
        <Tabs.Root defaultValue="profile" variant="enclosed">
          <Tabs.List w="full" bg="#f3f4f6" p={2}>
            <SimpleGrid columns={{ base: 2, sm: 3 }} gap={2} w="full">
              <Tabs.Trigger value="profile" w="full">
                <HStack gap={2}>
                  <FiUser size={16} />
                  <Text>Profile</Text>
                </HStack>
              </Tabs.Trigger>
              <Tabs.Trigger value="payment" w="full">
                <HStack gap={2}>
                  <FiCreditCard size={16} />
                  <Text>Payment</Text>
                </HStack>
              </Tabs.Trigger>
              {/* <Tabs.Trigger value="bookings" w="full">
                <HStack gap={2}>
                  <FiCalendar size={16} />
                  <Text>Bookings</Text>
                </HStack>
              </Tabs.Trigger> */}
              <Tabs.Trigger value="settings" w="full">
                <HStack gap={2}>
                  <FiSettings size={16} />
                  <Text>Settings</Text>
                </HStack>
              </Tabs.Trigger>
            </SimpleGrid>
          </Tabs.List>
          <Tabs.Content value="profile" pt={6}>
            <ProfileContent clientData={clientData} refetch={refetch} />
          </Tabs.Content>
          <Tabs.Content value="payment" pt={6}>
            <PaymentContent />
          </Tabs.Content>
          {/* <Tabs.Content value="bookings" pt={6}>
            <BookingContent />
          </Tabs.Content> */}
          <Tabs.Content value="settings" pt={6}>
            <SettingContent />
          </Tabs.Content>
        </Tabs.Root>
      </Container>
    </Box>
  );
}

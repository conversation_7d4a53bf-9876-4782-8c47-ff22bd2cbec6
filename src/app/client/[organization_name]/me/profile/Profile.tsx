'use client';
import { useGetClientByEmailQueryCM } from '@/api/clients/module/get-client-by-email';
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import PageLoader from '@/components/elements/loader/PageLoader';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useCallback } from 'react';
import Component from './ProfileUi';

export default function Profile({ organization_name }: any) {
  const session = useSupabaseSession();
  const { data: OrganizationData, isLoading: OrganizationLoading } =
    useGetOrganizationBySlugQuery({
      slug: organization_name,
      isPublic: 'true',
    });

  const {
    data: ClientData,
    isLoading: ClientLoading,
    refetch: refetchClientData,
  } = useGetClientByEmailQueryCM(
    {
      email: String(session?.user?.email),
      organization_id: OrganizationData?.[0]?.id,
    },
    {
      enabled:
        Boolean(OrganizationData?.[0]?.id) &&
        Boolean(String(session?.user?.email)),
    }
  );

  console.log('data-profile', ClientData);

  const refetch = useCallback(async () => {
    await refetchClientData();
  }, [refetchClientData]);

  if (ClientLoading || OrganizationLoading) {
    return <PageLoader />;
  }
  return (
    <div>
      <Component clientData={ClientData} refetch={refetch} />
    </div>
  );
}
//build a client profile screen for a sesion booking platform, it should have a tab for managing card details
//the users would be able to add their debit cards and remove it and manage it

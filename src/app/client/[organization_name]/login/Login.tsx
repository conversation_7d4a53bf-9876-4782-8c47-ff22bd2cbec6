'use client';

import Logo from '@/components/elements/logo/Logo';
import { Button } from '@/components/ui/button';
import {
  Box,
  Container,
  Field,
  Heading,
  Input,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';
import { FormEvent, useState } from 'react';
//import { toaster } from '@/components/ui/toaster';
// import { ToastMessages } from '@/constants/toast-messages';
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import PageLoader from '@/components/elements/loader/PageLoader';
import { useRouter } from 'next/navigation';
import { MdArrowForward } from 'react-icons/md';

export default function Login({
  organization_name,
}: {
  organization_name: string;
}) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { data: OrganizationData, isLoading: OrganizationLoading } =
    useGetOrganizationBySlugQuery(
      {
        slug: organization_name,
        isPublic: 'true',
      },
      { enable: <PERSON><PERSON><PERSON>(organization_name) }
    );

  console.log('OrganizationData', OrganizationData);
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      const payload = {
        email,
        slug: OrganizationData?.[0]?.slug,
        organization_id: OrganizationData?.[0]?.id,
      };
      //console.log('payload is ', payload);

      const response = await fetch(`/api/clients/login`, {
        method: 'POST',
        body: JSON.stringify(payload),
      });
      //const data =
      await response.json();
      if (!response.ok) {
        // toaster.create({
        //   description: data?.message,
        //   type: 'error',
        // });
        //console.log(data?.message);
      }
      // toaster.create({
      //   description: 'Please check your email for login link',
      //   type: 'success',
      // });
      router.replace(`/client/${organization_name}/check-email`);
    } catch (error: any) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  if (OrganizationLoading) {
    return <PageLoader />;
  }

  return (
    <Container maxW="lg" py={{ base: '12', md: '24' }}>
      <Stack gap="4">
        <VStack>
          <Logo isPublic={true} src={OrganizationData?.[0]?.logo_url} />
        </VStack>
        <Box
          border="1px solid"
          borderColor={'#e5e7eb'}
          //shadow={'md'}
          rounded={'lg'}
          py={'5'}
          px={{ base: '3', md: '5' }}
        >
          <VStack gap="">
            <Heading fontSize="22px" color="#111827" textAlign={'center'}>
              {OrganizationData?.[0]?.name || 'Company Name'} Client Portal
            </Heading>
            <Text
              color="#374151"
              fontSize={{ base: 'sm', md: 'md' }}
              //fontWeight={'500'}
              textAlign={'center'}
            >
              Access your bookings, invoices and receipts
            </Text>
          </VStack>
          <Box
            bg={'orange.50'}
            border="1px solid"
            borderColor={'orange.300'}
            rounded={'md'}
            px={'3'}
            py={'2'}
            mt={'7'}
          >
            <Text fontSize={{ base: '11px', md: '14px' }} color={'orange.700'}>
              <span style={{ fontWeight: '600' }}>
                Login using the email you use when booking your sessions
              </span>
            </Text>
          </Box>
          <form onSubmit={handleSubmit}>
            <Stack gap="4" mt={'7'}>
              <Field.Root>
                <Field.Label
                  color="#374151"
                  fontSize={{ base: 'sm', md: 'md' }}
                  fontWeight={'500'}
                >
                  Email
                </Field.Label>
                <Input
                  type="email"
                  required={true}
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  borderColor="#e5e7eb"
                />
              </Field.Root>
              <Button loading={loading} type="submit" bgColor={'#e97a5b'}>
                Continue <MdArrowForward size={12} />
              </Button>
            </Stack>
          </form>
        </Box>
      </Stack>
    </Container>
  );
}

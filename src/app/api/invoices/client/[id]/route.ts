import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getNumberParam } from '@/utils/format-object';
import { generateNameVariations } from '@/utils/helper';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseServer();
  const { id } = params;

  // Extract showAll parameter from URL
  const url = new URL(request.url);
  const showAll = url.searchParams.get('all') === 'true';

  // Pagination params with validation
  const currentPage = getNumberParam(url.searchParams, 'page_number', 1);
  const itemsPerPage = getNumberParam(url.searchParams, 'items_per_page', 50);

  // Validate pagination bounds
  if (currentPage < 1) {
    return NextResponse.json(
      { error: 'page_number must be greater than 0' },
      { status: 400 }
    );
  }

  if (itemsPerPage < 1 || itemsPerPage > 100) {
    return NextResponse.json(
      { error: 'items_per_page must be between 1 and 100' },
      { status: 400 }
    );
  }

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage - 1;

  try {
    // Validate that id is a valid number and convert it
    const numericId = Number(id);
    if (!id || isNaN(numericId)) {
      return NextResponse.json({ error: 'Invalid client ID' }, { status: 400 });
    }

    // First, determine the parent ID to use for querying
    let parentId = numericId;
    const allClientIds = new Set([numericId]);

    // Check if this client is a linked client (child → parent)
    const { data: linkedClientCheck, error: linkedError } = await supabase
      .from('linked_clients')
      .select('client_id, added_client_id')
      .eq('added_client_id', numericId)
      .single();

    //Check if client is a parent (has children)
    const { data: parentLinkCheck } = await supabase
      .from('linked_clients')
      .select('client_id')
      .eq('client_id', numericId)
      .maybeSingle();

    const shouldUseNameVariations =
      (!linkedError && !!linkedClientCheck) || !!parentLinkCheck;

    if (linkedClientCheck && !linkedError) {
      parentId = linkedClientCheck.client_id;
      allClientIds.add(parentId);
    }

    //query using the parent ID (since parent has all invoices)
    const query = supabase
      .from(tableNames.invoices)
      .select(`*, clients(display_name, first_name, last_name), slp_notes(*)`, {
        count: 'exact',
      })
      .in('client_id', Array.from(allClientIds))
      .filter('invoice_date', 'not.is', null)
      .order('invoice_date', { ascending: false })
      .range(startIndex, endIndex);

    const { data, error, count } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // If showAll is true, return ALL invoices from parent without filtering
    if (showAll) {
      return NextResponse.json({
        data: data || [],
        pagination: {
          page_number: currentPage,
          total_count: count || 0,
          items_per_page: itemsPerPage,
        },
      });
    }

    let filteredInvoices = data || [];

    // If not showAll, filter the results by name matching
    // Get the specific client's info for name matching
    if (!showAll && shouldUseNameVariations) {
      const { data: clientInfo, error: clientError } = await supabase
        .from('clients')
        .select('display_name, first_name, last_name', { count: 'exact' })
        .eq('id', numericId)
        .single();

      if (clientError) {
        return NextResponse.json(
          { error: clientError.message },
          { status: 500 }
        );
      }

      // Generate name variations for filtering
      const nameVariations = generateNameVariations(
        clientInfo.display_name || '',
        clientInfo.first_name || '',
        clientInfo.last_name || ''
      );

      // Filter the data in memory by name matching
      filteredInvoices =
        data?.filter((invoice: any) => {
          // Check if invoice name matches any variation
          const invoiceName = invoice.name || '';
          return nameVariations.some(
            (variation) =>
              invoiceName === variation ||
              invoiceName.toLowerCase().includes(variation.toLowerCase())
          );
        }) || [];
    }

    return NextResponse.json({
      data: filteredInvoices,
      pagination: {
        page_number: currentPage,
        total_count: filteredInvoices.length,
        items_per_page: itemsPerPage,
      },
    });
  } catch (error) {
    console.error('Error in invoice API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

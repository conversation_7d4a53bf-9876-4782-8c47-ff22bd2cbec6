/* eslint-disable no-useless-escape */

import { findUserByRegistration } from '@/api/users';
import { createExternalApiLogs } from '@/app/service/api-logs';
import { env } from '@/constants/env';
import { KlaviyoActions, klaviyoMetric } from '@/constants/klaviyo-actions';
import { tableNames } from '@/constants/table_names';
import { dispatchKlaviyoEvent } from '@/lib/klaviyo/service';
import {
  // updateInvoice,
  checkDuplicate,
  // createInvoice,
  //insertIntoClients,
  linkInvoiceWithSlpNote,
  updateClient,
} from '@/reuseables/invoice/helpers';
import { checkIfEmailExist, createUser } from '@/utils/create-user';
import { addTimeToDate } from '@/utils/date-formatter';
import { convertToNumber } from '@/utils/helper';
import { getDuration } from '@/utils/num-format';
import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
// const supabase = createClient(env.SUPABASE_URL!, env.SUPABASE_ANON_KEY!);

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const updateInvoice = async (payload: any, id: any, supabase: any) => {
  if (!id) throw new Error('Missing invoice id');

  const { data, error } = await supabase
    .from(tableNames.invoices)
    .update(payload)
    .eq('id', Number(id))
    .select();
  if (error) throw error;
  return data;
};
const saveRawData = async (payload: any) => {
  const { data } = await supabase
    .from(tableNames.invoices)
    .insert({ raw_data: payload })
    .select();
  return data;
};
export async function POST(req: NextRequest) {
  const body = await req.json();
  console.log('BODY LOGGED IS ', body, ' ENDS HERE');

  try {
    const data_to_insert: any = {};
    let invoice_id;
    let slpData;

    if (body) {
      await createExternalApiLogs(
        {
          source: 'quickbooks',
          domain: 'invoice',
          raw_data: body,
        },
        supabase
      );
      const duplicate = await checkDuplicate(
        body.email?.toLowerCase(),
        body.invoice_number,
        body.invoice_date,
        supabase
      );
      if (duplicate.data && duplicate.data.length > 0) {
        //console.log('Duplicate detected, exiting.', data_to_insert);
        NextResponse.json(data_to_insert);
      } else {
        // Start by saving Raw Data to the the invoices table
        const initialInvoice: any = await saveRawData(body);
        //console.log('initial invoice is ', initialInvoice);

        invoice_id = initialInvoice?.[0]?.id;

        // Save basic data to the invoice
        data_to_insert.name = body.name;
        data_to_insert.email = body.email?.toLowerCase();
        data_to_insert.product = body.product;
        data_to_insert.total_price = body.total_price;
        data_to_insert.memo = body.memo;
        data_to_insert.invoice_number = body.invoice_number;
        data_to_insert.qty = body.qty;
        data_to_insert.data_source = 'qbo_zapier';

        // Create invoices with immediate info
        //console.log('trying to create invoice');

        //Update the basic info
        await updateInvoice(data_to_insert, invoice_id, supabase);
        //console.log('invoice created');

        // invoice_id = invoice[0]?.id;
        //console.log('Invoice id: ', invoice_id);

        // Now, after the invoice has been created successfully, we will process the invoice_date
        data_to_insert.invoice_date_raw = body.invoice_date;
        data_to_insert.invoice_date = addTimeToDate(body.invoice_date, 6, 0);
        // data_to_insert.invoice_date = new Date(body.invoice_date).toISOString();

        const duration_regex: any = /[\.\d]+\s[hH]our|\d+\s[mM]inutes/;
        const durationString = duration_regex.exec(body.product)[0];
        data_to_insert.duration = getDuration(durationString);
        data_to_insert.interval = getDuration(durationString);
        const registration_regex: any = /\w+\s\#\d+/;

        data_to_insert.registration = registration_regex.exec(body.product)[0];

        //  Trying to insert slp_id
        try {
          //console.log('finding user by registration');

          slpData = await findUserByRegistration(
            supabase,
            data_to_insert.registration
          );
          // console.log('seen user by registration', slpData);

          if (slpData && slpData.length > 0) {
            data_to_insert.slp_id = slpData[0].id;
            data_to_insert.organization_id = slpData[0].organization_id;
            data_to_insert.slp = `${slpData[0]?.first_name} ${slpData[0]?.last_name}`;
          } else {
            console.error('User not found');
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }

        if (
          convertToNumber(data_to_insert.interval) > 0 &&
          convertToNumber(data_to_insert.qty) > 0
        ) {
          data_to_insert.total_hours =
            convertToNumber(data_to_insert.interval) *
            convertToNumber(data_to_insert.qty);
        }

        const ax_regex = /(ssessment)/;
        const tx_regex = /(herapy|reatment|raining)/;
        const workshop_regex = /(orkshop)/;

        if (ax_regex.test(body.product)) {
          data_to_insert.session_type = 'Ax';
        } else if (workshop_regex.test(body.product)) {
          data_to_insert.session_type = 'Workshop';
        } else if (tx_regex.test(body.product)) {
          data_to_insert.session_type = 'Tx';
          const package_regex: any = /(\d+\/\d+)/;
          data_to_insert.package = package_regex.test(body.product);
          if (data_to_insert.package) {
            const package_size_regex = /1\/(6|12)\b/;
            data_to_insert.package_size = package_size_regex.exec(
              body?.product as any
            )?.[1];
          }
        } else {
          data_to_insert.session_type = 'Other';
        }

        const late_cancellation_regex = /(LATE CANCELLATION)|(NO SHOW)/;
        data_to_insert.late_cancellation = late_cancellation_regex.test(
          body.product
        );
        const referral_regex = /REFERRAL/;
        data_to_insert.referral = referral_regex.test(body.product);

        // console.log(data_to_insert);
      }

      // Update all other linked tables
      // Check for existing client in client_emails
      // console.log('get client emails');

      // const { data } = (await supabase
      //   .from('client_emails')
      //   .select(`id, email, client_id, clients(*)`)
      //   .eq('email', data_to_insert.email?.toLowerCase())) as any;

      const data = await checkIfEmailExist(
        data_to_insert.email?.toLowerCase(),
        'id, email, client_id, clients(*)',
        false,
        supabase
      );
      // console.log('data from client emails is ', data);

      // If client email already exists, use existing client, and update status to customer
      if (data) {
        data_to_insert.client_id = data.client_id;

        if (data_to_insert.total_price > 0) {
          // console.log('Updating', data_to_insert.email);
          //console.log('updating clients');

          const { error } = await supabase
            .from('clients')
            .update({ stage: 'Customer', slp_notes: 'Active' })
            .eq('id', data_to_insert.client_id)
            .select();
          if (error) throw error;

          // dispatch event of stage changed
          if (data?.clients?.stage?.toLowerCase() !== 'customer') {
            //Dispatch Events
            await dispatchKlaviyoEvent(
              data_to_insert.organization_id,
              supabase,
              {
                data: {
                  type: 'event',
                  attributes: {
                    profile: {
                      data: {
                        type: 'profile',
                        attributes: {
                          email: data_to_insert?.email,
                          first_name: String(data_to_insert?.name)?.split(
                            ' '
                          )?.[0],
                          last_name: String(data_to_insert?.name)?.split(
                            ' '
                          )?.[1],
                          id: data_to_insert.client_id,
                          stage: 'Customer',
                        },
                      },
                    },
                    metric: {
                      data: {
                        type: 'metric',
                        attributes: {
                          name: 'Stage Updated',
                        },
                      },
                    },
                    properties: {
                      new_stage: 'Customer',
                    },
                  },
                },
              },
              KlaviyoActions.INVOICE_CREATED
            );
          }

          // Status of the entry in the followups table with the given id changed to "Complete" when an invoice is created
          //console.log('update followup');
          const { error: followupsError } = await supabase
            .from('followups')
            .update({ status: 'Completed' })
            .eq('client_id', data_to_insert.client_id)
            .eq('status', 'Incomplete');

          if (followupsError) throw followupsError;
        }
      }
      // Otherwise, create new client and client emails
      else {
        //console.log('Inserting', data_to_insert.email);
        const [firstName, ...lastNameParts] = (data_to_insert.name || '')
          .trim()
          .split(/\s+/);
        const lastName = lastNameParts.join(' ') || null;
        const clientPayload = {
          email: data_to_insert.email,
          first_name: firstName,
          last_name: lastName,
          display_name: data_to_insert.name,
          stage: data_to_insert.total_price > 0 ? 'Customer' : 'Prospect',
          slp_notes: 'Active',
          organization_id: data_to_insert.organization_id || 1,
        };

        const newClient = await createUser(
          clientPayload,
          false,
          false,
          supabase
        );
        //console.log('is there new client');

        if (newClient) {
          data_to_insert.client_id = newClient.id;

          const activitiesPayload = {
            client_id: newClient[0]?.id,
            activity_type: 'client_created',
            details: {
              created_by: 'quickbook',
            },
            activity_date: new Date().toISOString(),
            organization_id: newClient[0]?.organization_id,
          };
          //console.log('creating activity');

          const { error: activityUploadError } = await supabase
            .from(tableNames.client_activities)
            .insert(activitiesPayload);

          if (activityUploadError) {
            throw activityUploadError;
          }
        }
      }
      // data_to_insert.id = invoice_id;

      // update the actual client
      await updateClient(data_to_insert, supabase);
      // console.log('Final data to update', data_to_insert);
      const updatedInvoice = await updateInvoice(
        data_to_insert,
        invoice_id,
        supabase
      );
      //Dispatch Events
      await dispatchKlaviyoEvent(
        data_to_insert.organization_id,
        supabase,
        {
          data: {
            type: 'event',
            attributes: {
              profile: {
                data: {
                  type: 'profile',
                  attributes: {
                    email: data_to_insert?.email,
                    first_name: String(data_to_insert?.name)?.split(' ')?.[0],
                    last_name: String(data_to_insert?.name)?.split(' ')?.[1],
                    id: data_to_insert.client_id,
                  },
                },
              },
              metric: {
                data: {
                  type: 'metric',
                  attributes: {
                    name: klaviyoMetric.invoiceCreated,
                  },
                },
              },
              properties: {
                invoice_data: new Date(
                  data_to_insert?.invoice_date
                ).toISOString(),
                total_price: data_to_insert?.total_price,
                event_name: data_to_insert.product,
              },
              value: data_to_insert?.total_price,
              value_currency:
                slpData?.[0]?.organization?.currency_code || 'usd',
            },
          },
        },
        KlaviyoActions.INVOICE_CREATED
      );

      // await createActivity(updatedInvoice[0]);
      //TODO: Link package to the invoice
      await linkInvoiceWithSlpNote(updatedInvoice[0], supabase);
      return NextResponse.json(data_to_insert);
    }
  } catch (error: any) {
    console.error(error);
    return NextResponse.json({ message: error.message });
  }
}

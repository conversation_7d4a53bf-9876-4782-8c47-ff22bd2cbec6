import { stripe } from '@/utils/stripe';

import { checkoutSessionCompleted } from './checkout-session';
import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { handlePaymentIntentSucceed } from './handle-payment-intent-succeed';
import { createExternalApiLogs } from '@/app/service/api-logs';
import { createClient } from '@supabase/supabase-js';
import { env } from '@/constants/env';

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  // const body = await request.json();
  const rawBody = await request.text();
  await createExternalApiLogs(
    {
      source: 'stripe',
      domain: 'transaction',
      raw_data: rawBody,
    },
    supabase
  );
  const signature = headers().get('stripe-signature');

  const sig = request.headers.get('stripe-signature') as string;
  // console.log('sig IN STRIPE WEBHOOK IS ', sig);
  // console.log('signature IN STRIPE WEBHOOK IS ', signature);
  // console.log('BODY IN STRIPE WEBHOOK IS ', rawBody);
  // console.log('endpointSecret  IS ', endpointSecret);

  let event;
  try {
    event = stripe.webhooks.constructEvent(
      rawBody,
      sig || (signature as string),
      endpointSecret
    );
  } catch (err: any) {
    console.error('Webhook signature verification failed', err);
    return NextResponse.json(
      { error: `Webhook Error: ${err.message}` },
      { status: 400 }
    );
  }

  switch (event.type) {
    case 'checkout.session.completed': {
      const result = await checkoutSessionCompleted(event);
      if (result.success) {
        return NextResponse.json({ received: true }, { status: 200 });
      } else {
        return NextResponse.json({ error: result.message }, { status: 400 });
      }
    }
    case 'payment_intent.succeeded': {
      const pi = event.data.object;
      const result = await handlePaymentIntentSucceed(pi);
      if (result.success) {
        return NextResponse.json({ received: true }, { status: 200 });
      } else {
        return NextResponse.json({ error: result.message }, { status: 400 });
      }
    }
    default:
      break;
  }
}

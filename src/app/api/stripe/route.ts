import { createExternalApiLogs } from '@/app/service/api-logs';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
// import { extractSessionDetails } from '@/utils/helper';
import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const organization_id = 1;
// const supabase = createClient(env.SUPABASE_URL!, env.SUPABASE_ANON_KEY!);
const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const parseTrainingPackage = (input: string) => {
  // Case 1 & 3 pattern: "6 x 0.5hr" or "12 x 0.5hr"
  const case1Match = input.match(/(\d+)\s*x\s*(\d+(?:\.\d+)?)\s*hr/i);

  // Case 2 pattern: "0.5hx6"
  const case2Match = input.match(/(\d+(?:\.\d+)?)h\s*x?\s*(\d+)/i);

  if (case1Match) {
    return {
      size: parseInt(case1Match[1], 10),
      hours: parseFloat(case1Match[2]),
    };
  }

  if (case2Match) {
    return {
      size: parseInt(case2Match[2], 10),
      hours: parseFloat(case2Match[1]),
    };
  }

  return {
    size: null,
    hours: null,
  };
};
const saveRawData = async (payload: any) => {
  const { data } = await supabase
    .from(tableNames.packages)
    .insert({ raw_data: payload })
    .select();
  return data;
};
export async function POST(req: NextRequest) {
  const body = await req.json();
  let packageId;
  const data_to_insert: any = {};
  try {
    if (body) {
      //console.log('BODY LOGGED IS ', body, ' ENDS HERE');
      const initialPackage = await saveRawData(body);
      await createExternalApiLogs(
        {
          source: 'stripe',
          domain: 'package',
          raw_data: body,
        },
        supabase
      );
      packageId = initialPackage?.[0]?.id;

      data_to_insert.product = body.product;
      data_to_insert.name = body.name;
      data_to_insert.email = body.email.toLowerCase();
      data_to_insert.total = body.total;
      data_to_insert.stripe_id = body.stripe_id;
      data_to_insert.transaction_dt = body.transaction_dt;
      data_to_insert.source = body.source || 'Stripe';
      data_to_insert.status = body.status || 'ACTIVE';
      data_to_insert.organization_id = organization_id;
      data_to_insert.payment_method = 'Stripe';
    } else {
      //console.log('Something went wrong', body);
      throw new Error();
    }

    // Search for existing client using email
    const findExistingClient = async () => {
      const { data, error } = await supabase
        .from('client_emails')
        .select(`client_id, email`)
        .ilike('email', data_to_insert.email);

      if (error) throw error;
      return data;
    };

    const existingClient = await findExistingClient();
    //console.log('existing client', existingClient);

    // If the email exists in client_emails, a corresponding client should already exist
    if (existingClient && existingClient[0]) {
      //console.log('Client Found', existingClient[0]);
      data_to_insert.client_id = existingClient[0].client_id;
    } else if (!existingClient || existingClient.length == 0) {
      // Create a new client + clientEmails
      // console.log(
      //   'Client not found, creating new client',
      //   data_to_insert.email
      // );

      // Create client object
      // let client_obj = { email: data_to_insert.email };

      // Task: ensure utm_ fields are inserted into clients table in calendly.js
      const client_obj: any = {
        // Include UTM fields if they exist in data_to_insert
        utm_campaign: data_to_insert.utm_campaign || null, // Use null if not present
        utm_source: data_to_insert.utm_source || null, // Use null if not present
        utm_medium: data_to_insert.utm_medium || null, // Use null if not present
        utm_content: data_to_insert.utm_content || null, // Use null if not present
        organization_id,
        display_name: data_to_insert.name,
      };

      if (data_to_insert.first_name)
        client_obj.first_name = data_to_insert.first_name;
      if (data_to_insert.last_name)
        client_obj.last_name = data_to_insert.last_name;
      if (data_to_insert.phone) client_obj.phone = data_to_insert.phone;
      if (data_to_insert.province)
        client_obj.province = data_to_insert.province;
      if (data_to_insert.appointment) {
        client_obj.lead_created = data_to_insert.appointment;
        client_obj.last_consultation_date = data_to_insert.appointment;
      }

      const createClient = async () => {
        const { data, error } = await supabase
          .from('clients')
          .insert(client_obj)
          .select();

        //console.log('New Client Record', data);
        if (error) throw error;
        return data;
      };

      const newClient = await createClient();

      if (newClient) {
        data_to_insert.client_id = newClient[0].id;

        const createClientEmails = async (client_id: any) => {
          const { data } = await supabase
            .from('client_emails')
            .insert({
              client_id: client_id,
              email: data_to_insert.email,
              organization_id,
            })
            .select();

          return data;
        };
        //const newClientEmails =
        await createClientEmails(newClient[0].id);

        const activitiesPayload = {
          client_id: newClient[0]?.id,
          activity_type: 'client_created',
          details: {
            created_by: 'stripe',
          },
          organization_id,
        };
        const { error: activityUploadError } = await supabase
          .from(tableNames.client_activities)
          .insert(activitiesPayload);

        if (activityUploadError) {
          throw activityUploadError;
        }

        //console.log('New Client Emails', newClientEmails);
      }
    } else {
      //console.log('No Client Actions taken');
    }

    // Check for duplicates
    const duplicate = await supabase
      .from('packages')
      .select(`*`)
      .eq('stripe_id', data_to_insert.stripe_id);
    const balanceRegex = parseTrainingPackage(data_to_insert.product);

    if (balanceRegex.hours && balanceRegex.size) {
      data_to_insert.package_size = Number(balanceRegex.size);
      data_to_insert.balance = Number(balanceRegex.size);
      data_to_insert.session_quantity = Number(balanceRegex.size);
      data_to_insert.session_duration = Number(balanceRegex.hours) * 60;
    }

    // return;
    if (duplicate.data && duplicate.data.length == 0) {
      //console.log('data_to_insert: ', data_to_insert);
      const { error } = await supabase
        .from('packages')
        .update(data_to_insert)
        .eq('id', packageId);
      if (error) throw error;
      //console.log('Packages inserts complete');
    }

    return NextResponse.json(duplicate);
  } catch (error: any) {
    console.error('Error', error);
    return NextResponse.json({ message: error.message });
  }
}

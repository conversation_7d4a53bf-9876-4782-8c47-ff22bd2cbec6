import { findUserByEmail } from '@/api/users';
import { createExternalApiLogs } from '@/app/service/api-logs';
import { env } from '@/constants/env';
import { KlaviyoActions, klaviyoMetric } from '@/constants/klaviyo-actions';
import { tableNames } from '@/constants/table_names';
import { dispatchKlaviyoEvent } from '@/lib/klaviyo/service';
import { checkIfEmailExist, createUser } from '@/utils/create-user';
import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// const supabase = createClient(env.SUPABASE_URL!, env.SUPABASE_ANON_KEY!);

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const saveRawData = async (data: any) =>
  await supabase.from(tableNames.bookings).insert({ raw_data: data }).select();

export async function POST(req: NextRequest) {
  const body = await req.json();
  console.log('Calendly Payload: ', body);

  const data_to_insert: any = {};
  try {
    if (!body) {
      throw new Error('Missing Payload');
    }
    console.log('BODY LOGGED IS ', body, ' ENDS HERE');

    const initialBooking = await saveRawData(body);
    await createExternalApiLogs(
      {
        source: 'calendly',
        domain: 'booking',
        raw_data: body,
      },
      supabase
    );
    //console.log('Created Booking: ', initialBooking?.data?.[0]);
    const initialBookingId = initialBooking?.data?.[0]?.id;

    data_to_insert.calendly_event_type = body.event;
    data_to_insert.created_at = body.payload.created_at;
    // add the created_at to the booking_created_at_raw column as well to store the raw created at time
    data_to_insert.booking_created_at_raw = body.payload.created_at;
    data_to_insert.first_name = body.payload.first_name;
    data_to_insert.last_name = body.payload.last_name;
    data_to_insert.email = body.payload.email.toLowerCase();
    data_to_insert.start_time = body.payload.scheduled_event?.start_time;
    data_to_insert.end_time = body.payload.scheduled_event?.end_time;

    const calendly_event_uuid = body.payload.event.split('/').pop();
    const calendly_event_data_settings = {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_CALENDLY_KEY}`,
      },
    };
    const calendly_event_data_req = await fetch(
      `https://api.calendly.com/scheduled_events/${calendly_event_uuid}`,
      calendly_event_data_settings
    );
    const calendly_event_data = await calendly_event_data_req.json();

    // console.log('calendly_event_data', calendly_event_data);

    // console.log('body.payload', body.payload);
    if (body.payload.tracking) {
      data_to_insert.utm_campaign = body.payload.tracking.utm_campaign;
      data_to_insert.utm_source = body.payload.tracking.utm_source;
      data_to_insert.utm_medium = body.payload.tracking.utm_medium;
      data_to_insert.utm_content = body.payload.tracking.content;
    }
    if (body.payload.questions_and_answers) {
      data_to_insert.phone =
        body.payload.questions_and_answers[0] &&
        body.payload.questions_and_answers[0].answer;
      data_to_insert.province =
        body.payload.questions_and_answers[1] &&
        body.payload.questions_and_answers[1].answer;
    }
    // console.log('calendly_event_data is ', calendly_event_data);
    if (calendly_event_data && calendly_event_data.resource) {
      data_to_insert.appointment = calendly_event_data.resource.start_time;
      data_to_insert.assigned_to =
        calendly_event_data.resource.event_memberships[0].user_email;

      data_to_insert.event = calendly_event_data.resource.name;
      try {
        const slpData = await findUserByEmail(
          supabase,
          calendly_event_data.resource.event_memberships[0].user_email
        );
        if (slpData && slpData.length > 0) {
          data_to_insert.slp_id = slpData[0].id;
          data_to_insert.organization_id = slpData[0].organization_id;
        } else {
          console.error('User not found');
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    }

    const existingClient = await checkIfEmailExist(
      data_to_insert.email,
      'client_id, email',
      false,
      supabase
    );

    console.log('existing client', existingClient);

    // If the email exists in client_emails, a corresponding client should already exist
    if (existingClient) {
      //console.log('Client Found', existingClient[0]);
      data_to_insert.client_id = existingClient.client_id;
    } else if (existingClient === null) {
      // Create a new client + clientEmails

      // Task: ensure utm_ fields are inserted into clients table in calendly.js
      const client_obj: any = {
        organization_id: data_to_insert.organization_id,
        // Include UTM fields if they exist in data_to_insert
        utm_campaign: data_to_insert.utm_campaign || null, // Use null if not present
        utm_source: data_to_insert.utm_source || null, // Use null if not present
        utm_medium: data_to_insert.utm_medium || null, // Use null if not present
        utm_content: data_to_insert.utm_content || null, // Use null if not present
        referral_source:
          (body.payload.questions_and_answers[2] &&
            body.payload.questions_and_answers[2].answer) ||
          null,
        email: data_to_insert.email.toLowerCase(),
      };

      if (data_to_insert.first_name)
        client_obj.first_name = data_to_insert.first_name;
      if (data_to_insert.last_name)
        client_obj.last_name = data_to_insert.last_name;
      if (data_to_insert.phone) client_obj.phone = data_to_insert.phone;
      if (data_to_insert.province)
        client_obj.province = data_to_insert.province;
      if (data_to_insert.first_name && data_to_insert.last_name) {
        client_obj.display_name = `${data_to_insert.first_name} ${data_to_insert.last_name}`;
      }
      if (data_to_insert.appointment) {
        client_obj.lead_created = new Date(data_to_insert.appointment);
        client_obj.last_consultation_date = new Date(
          data_to_insert.appointment
        );
      }

      console.log('client_obj', client_obj);

      const newClient = await createUser(client_obj, false, false, supabase);

      if (newClient) {
        data_to_insert.client_id = newClient.id;

        const activitiesPayload = {
          client_id: newClient?.id,
          activity_type: 'client_created',
          details: {
            created_by: 'calendly',
          },
          organization_id: data_to_insert.organization_id,
          activity_date: new Date().toISOString(),
        };
        const { error: activityUploadError } = await supabase
          .from(tableNames.client_activities)
          .insert(activitiesPayload);
        // console.log('ln 145', client_obj);

        if (activityUploadError) {
          throw activityUploadError;
        }
      }
    } else {
      console.log('No Client Actions taken');
    }

    // add the appointment to the appointment_raw column as well to store the raw appointment
    data_to_insert.appointment_raw = data_to_insert.appointment;

    // Add this right after you define data_to_insert and before saving to the database
    if (data_to_insert.calendly_event_type === 'invitee.canceled') {
      // Delete any existing bookings with the same details
      const { error: deleteError } = await supabase
        .from('bookings')
        .delete()
        .match({
          client_id: data_to_insert.client_id,
          slp_id: data_to_insert.slp_id,
          appointment: data_to_insert.appointment,
          event: data_to_insert.event,
        });

      if (deleteError) {
        console.error(
          'Error deleting duplicate canceled booking:',
          deleteError
        );
        throw deleteError;
      }
      //console.log('Deleted duplicate canceled booking');
    }
    //console.log('data_to_insert: ', data_to_insert);
    const { error } = await supabase
      .from('bookings')
      .update(data_to_insert)
      .eq('id', initialBookingId);
    if (error) throw error;

    // To be removed later after implementation of rules
    if (
      (data_to_insert.event &&
        data_to_insert?.event?.toLowerCase()?.includes('assessment')) ||
      data_to_insert?.event?.toLowerCase()?.includes('session')
    ) {
      const { error: clientError } = await supabase
        .from('clients')
        .update({ stage: 'Customer' })
        .eq('id', data_to_insert.client_id)
        .select();
      if (clientError) throw clientError;

      // Status of the entry in the followups table with the given id changed to "Complete" when an invoice is created
      const { error: followupsError } = await supabase
        .from('followups')
        .update({ status: 'Completed' })
        .eq('client_id', data_to_insert.client_id)
        .eq('status', 'Incomplete');

      if (followupsError) throw followupsError;
    }
    //Dispatch Events
    await dispatchKlaviyoEvent(
      data_to_insert.organization_id,
      supabase,
      {
        data: {
          type: 'event',
          attributes: {
            profile: {
              data: {
                type: 'profile',
                attributes: {
                  email: data_to_insert?.email,
                  first_name: data_to_insert?.first_name,
                  last_name: data_to_insert?.last_name,
                  id: data_to_insert.client_id,
                },
                // id: String(values?.client_id),
              },
            },

            metric: {
              data: {
                type: 'metric',
                attributes: {
                  name: klaviyoMetric.bookingCreated,
                },
              },
            },
            properties: {
              booking_date: new Date(data_to_insert?.appointment).toISOString(),
              client_province: data_to_insert?.province,
              booking_name: data_to_insert?.event,
            },
          },
        },
      },
      KlaviyoActions.BOOKING_CREATED
    );
    return NextResponse.json({ message: 'Calendly updated sucessfully' });
  } catch (error: any) {
    console.log('Error is ', error);
    return NextResponse.json({ message: error.message });
  }
}

import { env } from '@/constants/env';
import { createClient } from '@supabase/supabase-js';
import { google } from 'googleapis';
import moment from 'moment-timezone';
import { NextRequest, NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
const refreshAccessToken = async (refreshToken: string) => {
  try {
    //console.log('Tyring to refresh token');

    const oauth2Client = new google.auth.OAuth2(
      process.env.GMAIL_CLIENT_ID,
      process.env.GMAIL_SECRET_KEY,
      `${env.FRONTEND_URL}/auth/callback`
    );
    oauth2Client.setCredentials({ refresh_token: refreshToken });

    // Get new access token
    const { credentials } = await oauth2Client.refreshAccessToken();

    // Optionally, store the new access token in the database
    await supabaseAdmin
      .from('users')
      .update({ calendar_access_token: credentials.access_token })
      .eq('calendar_refresh_token', refreshToken);
    return credentials.access_token;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
};

export async function POST(request: NextRequest) {
  const body = await request.json();
  const { form, orgData, eventData, organizer_email } = body as any;
  let accessToken = orgData.owner.calendar_access_token || '';
  const refreshToken = orgData.owner.calendar_refresh_token;

  // console.log('Access token is ', accessToken);
  // console.log('refreshToken token is ', refreshToken);

  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({
    access_token: accessToken,
    refresh_token: refreshToken,
  });
  //console.log('trying to check token validation');

  // Check if token needs refreshing
  try {
    // Try getting token info
    const tokenInfo = await oauth2Client.getTokenInfo(accessToken);

    // console.log('token info is ', tokenInfo);

    if (tokenInfo.expiry_date < Date.now()) {
      //console.log('Access token expired, attempting refresh...');
      accessToken = await refreshAccessToken(refreshToken);
    }
  } catch (error: any) {
    console.error('Invalid or expired token, refreshing...', error.message);
    accessToken = await refreshAccessToken(refreshToken);
  }

  oauth2Client.setCredentials({ access_token: accessToken });

  const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

  // console.log('form.selectedDate is ', form.selectedDate);
  // console.log('form.selectedTime is ', form.selectedTime);
  // return;

  const selectedDateLocal = moment(form.selectedDate)
    .tz(form.timeZone)
    .format('YYYY-MM-DD');
  //console.log('selectedDateUTC is ', selectedDateLocal);

  const startDateTimeUTC = moment.tz(
    `${selectedDateLocal} ${form.selectedTime}`,
    'YYYY-MM-DD HH:mm',
    'UTC'
  );
  const endDateTime = startDateTimeUTC
    .clone()
    .add(eventData.duration, 'minutes');

  // console.log('start time is ', startDateTimeUTC);
  // console.log('endTime time is ', endDateTime);
  // return;

  // Enhanced description with form URL
  let eventDescription = eventData.description || '';

  if (eventData.linked_form_url) {
    eventDescription += `\n\n📋 Please complete this form before the meeting:\n${eventData.linked_form_url}`;
  }

  const event = {
    summary: eventData.title,
    location: 'Online',
    description: eventDescription,
    start: {
      dateTime: startDateTimeUTC.toISOString(),
      timeZone: form.timeZone,
    },
    end: {
      dateTime: endDateTime.toISOString(),
      timeZone: form.timeZone,
    },
    conferenceData: {
      createRequest: {
        requestId: `${eventData.id}-${Date.now()}`,
        conferenceSolutionKey: {
          type: 'hangoutsMeet', // This will create a Google Meet link
        },
      },
    },
    attendees: [
      { email: form.email },
      { email: organizer_email || orgData.owner.email },
    ],
    reminders: {
      useDefault: true,
    },
    // Optional: Add form URL as an attachment (alternative approach)
    attachments: eventData.linked_form_url
      ? [
          {
            fileUrl: eventData.linked_form_url,
            title: 'Pre-meeting Form',
            mimeType: 'text/html',
          },
        ]
      : undefined,
  };

  //console.log('event is ', event);
  // return;
  try {
    // Insert the event into Google Calendar
    const meetResponse = await calendar.events.insert({
      conferenceDataVersion: 1,
      calendarId: 'primary',
      requestBody: event as any,
      sendUpdates: 'all',
      supportsAttachments: eventData.linked_form_url ? true : false, // Enable attachments if form URL exists
    });

    const meetLink = meetResponse.data.hangoutLink;
    const googleEventId = meetResponse.data.id;
    // The meet link is available in the response
    // console.log('meetLink is ', meetLink);
    // console.log('meetLink is ', meetLink);
    // console.log('meetResponse is ', meetResponse.data);

    return NextResponse.json(
      { success: true, googleEventId, meetLink, data: meetResponse.data },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Error creating Google Meet event:', error);
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
}

import { env } from '@/constants/env';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import { google } from 'googleapis';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
const refreshAccessToken = async (refreshToken: string) => {
  try {
    //console.log('Tyring to refresh token');

    const oauth2Client = new google.auth.OAuth2(
      process.env.GMAIL_CLIENT_ID,
      process.env.GMAIL_SECRET_KEY,
      `${env.FRONTEND_URL}/auth/callback`
    );
    oauth2Client.setCredentials({ refresh_token: refreshToken });

    // Get new access token
    const { credentials } = await oauth2Client.refreshAccessToken();

    // Optionally, store the new access token in the database
    await supabaseAdmin
      .from('users')
      .update({ calendar_access_token: credentials.access_token })
      .eq('calendar_refresh_token', refreshToken);
    return credentials.access_token;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
};

export async function POST(request: Request) {
  const body = await request.json();
  const { event_id, refresh_token } = body as any;

  let access_token = body.access_token;
  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({
    access_token,
    refresh_token,
  });
  try {
    // Try getting token info
    const tokenInfo = await oauth2Client.getTokenInfo(access_token);

    // console.log('token info is ', tokenInfo);

    if (tokenInfo.expiry_date < Date.now()) {
      //console.log('Access token expired, attempting refresh...');
      access_token = await refreshAccessToken(refresh_token);
    }
  } catch (error: any) {
    console.error('Invalid or expired token, refreshing...', error.message);
    access_token = await refreshAccessToken(refresh_token);
  }

  try {
    const eventResponse = await fetch(
      `https://www.googleapis.com/calendar/v3/calendars/primary/events/${event_id}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${access_token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!eventResponse.ok) {
      throw new Error('Failed to fetch event');
    }
    const eventData = await eventResponse.json();
    //console.log('event data is ', eventData);

    return NextResponse.json({ success: true, eventData }, { status: 200 });
  } catch (error: any) {
    console.error('Error Getting event:', error);
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
}

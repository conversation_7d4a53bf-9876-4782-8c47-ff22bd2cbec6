import { env } from '@/constants/env';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import { google } from 'googleapis';
import moment from 'moment-timezone';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
const refreshAccessToken = async (refreshToken: string) => {
  try {
    //console.log('Tyring to refresh token');

    const oauth2Client = new google.auth.OAuth2(
      process.env.GMAIL_CLIENT_ID,
      process.env.GMAIL_SECRET_KEY,
      `${env.FRONTEND_URL}/auth/callback`
    );
    oauth2Client.setCredentials({ refresh_token: refreshToken });

    // Get new access token
    const { credentials } = await oauth2Client.refreshAccessToken();

    // Optionally, store the new access token in the database
    await supabaseAdmin
      .from('users')
      .update({ calendar_access_token: credentials.access_token })
      .eq('calendar_refresh_token', refreshToken);
    return credentials.access_token;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
};

function getMonthStartAndEnd(dateStr: any, timeZone: any) {
  // Step 1: Explicitly parse the input as UTC
  const utcDate = moment.utc(`${dateStr}T00:00:00Z`);
  //console.log('Input as UTC:', utcDate.format('YYYY-MM-DD HH:mm:ss [UTC]'));

  // Step 2: Get the month and year from the UTC date
  const year = utcDate.year();
  const month = utcDate.month(); // 0-based (e.g., 1 for February)

  // Step 3: Create start of the month in the specified time zone
  const startOfMonthLocal = moment
    .tz({ year, month, date: 1 }, timeZone)
    .startOf('day');
  // console.log(
  //   'Start of month (local):',
  //   startOfMonthLocal.format('YYYY-MM-DD HH:mm:ss Z')
  // );

  // Step 4: Create end of the month in the specified time zone
  const endOfMonthLocal = startOfMonthLocal
    .clone()
    .endOf('month')
    .add(1, 'month');
  // console.log(
  //   'End of month (local):',
  //   endOfMonthLocal.format('YYYY-MM-DD HH:mm:ss Z')
  // );

  // Step 5: Convert back to UTC for output
  return {
    startOfMonth: startOfMonthLocal.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
    endOfMonth: endOfMonthLocal.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
  };
}

export async function POST(request: Request) {
  const body = await request.json();
  const { timeZone, orgData, dateStr } = body as any;
  let access_token = orgData.owner.calendar_access_token || '';
  const refresh_token = orgData.owner.calendar_refresh_token;

  // console.log('time zone is ', timeZone);
  // console.log('dateStr is ', dateStr);

  // const startOfDay = `${dateStr}T00:00:00Z`;
  // const endOfDay = `${dateStr}T23:59:59Z`;

  // Get start and end of the month instead of a single day
  // const year = parseInt(dateStr.slice(0, 4), 10);
  // const month = parseInt(dateStr.slice(5, 7), 10) - 1; // Month is zero-based in JS

  // Get first and last day of the month
  const startOfMonth = getMonthStartAndEnd(dateStr, timeZone).startOfMonth;
  const endOfMonth = getMonthStartAndEnd(dateStr, timeZone).endOfMonth;

  // console.log('startOfMonth is ', startOfMonth);
  // console.log('endOfMonth is ', endOfMonth);
  // console.log('startOfDay is ', startOfDay);
  // console.log('endOfDay is ', endOfDay);
  // return;

  const calendar = google.calendar({ version: 'v3' });
  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({
    access_token,
    refresh_token,
  });
  try {
    // Try getting token info
    const tokenInfo = await oauth2Client.getTokenInfo(access_token);

    // console.log('token info is ', tokenInfo);

    if (tokenInfo.expiry_date < Date.now()) {
      //console.log('Access token expired, attempting refresh...');
      access_token = await refreshAccessToken(refresh_token);
    }
  } catch (error: any) {
    console.error('Invalid or expired token, refreshing...', error.message);
    access_token = await refreshAccessToken(refresh_token);
  }
  oauth2Client.setCredentials({ access_token });

  try {
    const response = await calendar.events.list({
      auth: oauth2Client,
      calendarId: 'primary',
      // timeMin: startOfDay,
      // timeMax: endOfDay,
      timeMin: startOfMonth,
      timeMax: endOfMonth,
      timeZone,
      singleEvents: true,
      orderBy: 'startTime',
    });
    // console.log('response from booked time is ', response.data);
    // console.log(JSON.stringify(response.data, null, 2)); // Pretty print with 2 spaces for indentation

    const data =
      response.data.items
        ?.filter((item) => item.transparency !== 'transparent')
        ?.map((event) => ({
          start: event?.start?.dateTime,
          end: event?.end?.dateTime,
        })) || [];
    // console.log('data is ', data);

    return NextResponse.json({ success: true, data }, { status: 200 });
  } catch (error: any) {
    console.error('Error Getting event:', error);
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
}

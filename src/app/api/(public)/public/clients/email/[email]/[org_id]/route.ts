import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(
  request: Request,
  { params }: { params: { email: string; org_id: string } }
) {
  const { email, org_id } = params;

  const { data, error } = await supabaseAdmin
    .from(tableNames.client_emails)
    .select(`*, client:clients!client_emails_client_id_fkey(*)`)
    .eq('email', email)
    .eq('organization_id', Number(org_id))
    .maybeSingle();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  if (!data) {
    return NextResponse.json(null, {
      headers: {
        'Cache-Control':
          'no-store, no-cache, must-revalidate, proxy-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
        'Surrogate-Control': 'no-store',
      },
    });
  }

  const { data: allEmails, error: emailsError } = await supabaseAdmin
    .from(tableNames.client_emails)
    .select('*')
    .eq('client_id', data.client_id)
    .eq('organization_id', Number(org_id));

  if (emailsError) {
    return NextResponse.json({ error: emailsError.message }, { status: 500 });
  }

  const response = {
    ...data,
    client_emails: allEmails || [],
  };

  return NextResponse.json(response, {
    headers: {
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
      'Surrogate-Control': 'no-store',
    },
  });
}

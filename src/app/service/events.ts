import { tableNames } from '@/constants/table_names';
import { IAvailability, IEvent } from '@/shared/interface/events';
import moment from 'moment-timezone';

// const deslugify = (slug: string): string => {
//   return slug.replace(/-/g, ' '); // Replace hyphens with spaces
// };

export async function getUserBuSlug(supabase: any, event_slug: any) {
  const { data, error } = await supabase
    .from(tableNames.users)
    .select('*')
    .eq('event_slug', event_slug);

  if (error) {
    console.error('Error fetching user:', error);
    return null;
  }
  const { data: organizationData, error: OrganizationError } = await supabase
    .from(tableNames.organizations)
    .select('*')
    .eq('id', data?.[0]?.organization_id);

  if (OrganizationError) {
    console.error('Error fetching organization:', error);
    return null;
  }
  return data.length > 0
    ? { ...data[0], organization: organizationData?.[0] }
    : null;
}
export async function getOrganizationByName(supabase: any, name: any) {
  const namePattern = name.replace(/[^a-zA-Z0-9]/g, '%');

  const { data, error } = await supabase
    .from(tableNames.organizations)
    .select('*, owner(*)')
    .ilike('name', `%${namePattern}%`)
    .limit(1);

  if (error) {
    console.error('Error fetching organization:', error);
    return null;
  }

  return data.length > 0 ? data[0] : null;
}
export async function getOrganizationByOrganizationSlug(
  supabase: any,
  slug: any
) {
  const { data, error } = await supabase
    .from(tableNames.organizations)
    .select('*, owner(*)')
    .ilike('slug', slug)
    .limit(1);

  if (error) {
    console.error('Error fetching organization:', error);
    return null;
  }

  return data.length > 0 ? data[0] : null;
}

export async function getEventsByCreatorSlug(supabase: any, creator_slug: any) {
  const { data, error } = await supabase
    .from(tableNames.events)
    .select('*')
    .eq('is_deleted', false)
    .eq('is_active', true)
    .eq('creator_slug', creator_slug);

  if (error) {
    console.error('Error fetching organization:', error);
    return null;
  }

  return data;
}
export async function getEventsByOrganizationName(supabase: any, name: any) {
  const { data, error } = await supabase
    .from(tableNames.events)
    .select('*')
    .ilike('organization_name', `%${name}%`)
    .eq('is_deleted', false)
    .eq('is_active', true);

  if (error) {
    console.error('Error fetching organization:', error);
    return null;
  }

  return data;
}

export async function getEventsById(supabase: any, id: any) {
  // console.log('slug is ', slug);

  const { data, error } = await supabase
    .from(tableNames.events)
    .select(
      `*, 
      availability:availabilities(id, day, start_time, end_time, available),
      service:services(*)
      `
    )
    .eq('id', Number(id))
    .eq('is_deleted', false)
    .eq('is_active', true);

  // console.log('data in get event by slug is ', data);

  if (error) {
    console.error('Error fetching event:', error);
    return null;
  }

  return data.length > 0 ? data[0] : null;
}
export async function getEventsBySlugAndCreator(
  supabase: any,
  slug: any,
  creator_slug: any
) {
  // console.log('slug is ', slug);

  const { data, error } = await supabase
    .from(tableNames.events)
    .select(
      `*,
      availability:availabilities(day, start_time, end_time, available),
      service:services(*)`
    )
    .eq('slug', slug)
    .eq('creator_slug', creator_slug)
    .eq('is_deleted', false)
    .eq('is_active', true);

  // console.log('data in get event by slug is ', data);

  if (error) {
    console.error('Error fetching event:', error);
    return null;
  }

  return data.length > 0 ? data[0] : null;
}

export async function generateAvailability(
  supabase: any,
  slug: any,
  currentDate: any,
  userTimeZone: string,
  orgData: any
) {
  const eventDetails = await getEventsBySlugAndCreator(
    supabase,
    slug,
    orgData?.owner?.event_slug
  );
  const { availability } = eventDetails as IEvent;
  // console.log('availability is ', availability);

  // const startDate = moment().startOf('day');
  // const endDate = moment(startDate).add(30, 'days');

  // const startDate = moment(currentDate).startOf('month');
  // const endDate = moment(currentDate).endOf('month');

  const startDate = moment.tz(currentDate, userTimeZone).startOf('month');
  const endDate = moment.tz(currentDate, userTimeZone).endOf('month');
  const tempDateStr = moment(currentDate).format('YYYY-MM-DD');

  const bookedSlotsRes = await fetch(
    `${process.env.NEXT_PUBLIC_SITE_URL}/api/public/google/get-booked-times`,
    {
      method: 'POST',
      body: JSON.stringify({
        timeZone: userTimeZone,
        orgData,
        dateStr: tempDateStr,
      }),
      headers: { 'Content-Type': 'application/json' },
    }
  );
  const bookedSlots = await bookedSlotsRes.json();
  //console.log('bookedSlots is ', bookedSlots);

  const availableDates = [];

  for (
    let date = startDate;
    date <= endDate;
    date = moment(date).add(1, 'days')
  ) {
    const dayOfTheWeek = moment(date).format('dddd').toLowerCase();

    const dayAvailability = availability?.find(
      (d: IAvailability) => d.day.toLowerCase() === dayOfTheWeek && d.available
    );

    if (dayAvailability) {
      const dateStr = moment(date).format('YYYY-MM-DD');

      const slots = await generateAvailableTimeSlots(
        dayAvailability.start_time,
        dayAvailability.end_time,
        eventDetails.duration,
        dateStr,
        bookedSlots
        // userTimeZone,
        // orgData
      );
      availableDates.push({ date: dateStr, slots });
    }
  }
  return availableDates;
}
export async function generateAvailableTimeSlots(
  startTime: any,
  endTime: any,
  duration: any,
  dateStr: any,
  bookedSlots: any
  // userTimeZone: any,
  // orgData: any
  // timeGap = 0
) {
  const slots = [];
  // console.log('start time is ', moment(startTime).utc().format('HH:mm'));
  // console.log('start time is ', startTime);
  const now = moment();

  // Parse the start time and end time
  const slotStartTime = moment(
    `${dateStr}T${moment(startTime).utc().format('HH:mm')}`
  );
  const slotEndTime = moment(
    `${dateStr}T${moment(endTime).utc().format('HH:mm')}`
  );

  // If the current time is later than the start time, set currentTime to now
  let currentTime = now.isAfter(slotStartTime) ? now : slotStartTime;

  // Round up the current time to the nearest duration interval
  const remainder = duration - (currentTime.minute() % duration);
  if (remainder !== duration) {
    currentTime.add(remainder, 'minutes').second(0).millisecond(0);
  }

  // console.log('i am getting booked slots ', bookedSlots);

  // console.log('bookedSlots is ', bookedSlots);
  // console.log('currentTime is ', currentTime);

  // const bookedSlots = await fetchBlockedTimes(dateStr, timeZone, accessToken);

  while (currentTime < slotEndTime) {
    // console.log('currentTime ', currentTime);
    // console.log('slotEndTime ', slotEndTime);
    const slotEnd = currentTime.clone().add(duration, 'minutes');
    const isBlocked = bookedSlots?.data?.some((event: any) => {
      const eventStart = moment(event.start).utc();
      const eventEnd = moment(event.end).utc();
      const currentTimeToCompare = currentTime.clone().utc(true);
      const slotEndToCompare = slotEnd.clone().utc(true);
      // console.log('in booked eventStart ', eventStart);
      // console.log('in booked eventEnddd ', eventEnd);
      // console.log(
      //   'return is ',
      //   currentTimeToCompare.isBetween(eventStart, eventEnd, null, '[)') ||
      //     slotEndToCompare.isBetween(eventStart, eventEnd, null, '(]')
      // );

      return (
        currentTimeToCompare.isBetween(eventStart, eventEnd, null, '[)') ||
        slotEndToCompare.isBetween(eventStart, eventEnd, null, '(]')
      );
    });
    if (!isBlocked) {
      slots.push(currentTime.format('HH:mm'));
    }

    currentTime = slotEnd;
  }

  return slots;
}

export async function getBookingByGoogleEventId(supabase: any, id: any) {
  // console.log('slug is ', slug);

  const { data, error } = await supabase
    .from(tableNames.bookings)
    .select(
      `*,
      slp:slp_id (*),
      event_data:event_id (*)`
    )
    .eq('google_event_id', id);

  if (error) {
    console.error('Error fetching Booking:', error);
    return null;
  }

  return data.length > 0 ? data?.[0] : null;
}
export async function getBookingById(supabase: any, id: any) {
  // console.log('slug is ', slug);

  const { data, error } = await supabase
    .from(tableNames.bookings)
    .select(
      `*,
      slp:slp_id (*),
      event_data:event_id (*)`
    )
    .eq('id', Number(id));

  if (error) {
    console.error('Error fetching event:', error);
    return null;
  }

  return data.length > 0 ? data?.[0] : null;
}
export async function getEventDetails(booking: any, user: any) {
  const eventRes = await fetch(
    `${process.env.NEXT_PUBLIC_SITE_URL}/api/public/google/event-info`,
    {
      method: 'POST',
      body: JSON.stringify({
        event_id: booking.google_event_id,
        refresh_token: user?.calendar_refresh_token,
        access_token: user?.calendar_access_token,
      }),
      headers: { 'Content-Type': 'application/json' },
    }
  );
  //console.log('client res is ', eventRes);

  const eventInfo = await eventRes.json();
  //console.log('eventInfo   is ', eventInfo);

  if (!eventRes.ok) {
    throw new Error(eventInfo.error || 'Failed to fetch organization details');
  }
  return eventInfo;
}

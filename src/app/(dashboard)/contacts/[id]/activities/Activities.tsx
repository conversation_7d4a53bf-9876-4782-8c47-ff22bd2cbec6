import AnimateLoader from '@/components/elements/loader/animate-loader';
import { activityType } from '@/constants/activity';
import { useGetUserProfile } from '@/hooks/admin/users/useGetProfile';
import { ActivityType } from '@/utils/enums';
import {
  Box,
  Center,
  Flex,
  Heading,
  HStack,
  Stack,
  Text,
} from '@chakra-ui/react';
import moment from 'moment';
import { useState } from 'react';
import { BsClock } from 'react-icons/bs';
import { CardWrapper } from '../profile/ActivityDetails';
import ActivityFilter from './ActivityFilter';
import BookingCreated from './BookingCreated';
import ClientCreated from './ClientCreated';
import ConsultationNotesCreated from './ConsultationNotesCreated';
import EmailCreated from './EmailCreated';
import FormSubmitted from './FormSubmitted';
import InvoiceCreated from './InvoiceCreated';
import LinkedPackages from './LinkedPackages';
import LinkedTransaction from './LinkedTransaction';
import NoteCreated from './NoteCreated';
import PackageCreated from './PackageCreated';
import ProfileUpdated from './ProfileUpdated';
import RefundedCreated from './RefundedCreated';
import SessionUpdated from './SessionUpdated';
import TransactionCreated from './TransactionCreated';
import { useActivities } from './useActivities';

export default function Activities({ data, handleSwitchTab }: any) {
  const { activities, ClientActivitiesLoading } = useActivities({ data });
  const user = useGetUserProfile();

  const [filter, setFilter] = useState<string>('all');
  // console.log('data', data);
  //console.log('activities--1', activities);

  // Filtering activities based on the selected filter
  const filteredActivities = activities?.filter((item: any) => {
    if (filter === 'all') return true;
    const type = item.activity_type?.toLowerCase().trim();

    const filterMap: { [key: string]: string[] } = {
      notes: [ActivityType.NOTE_CREATED, ActivityType.NOTE_MODIFIED],
      emails: ['email_sent', 'email_received'],
      invoice: [activityType.invoiceCreated],
      transaction: [activityType.transactionCreated],
      packages: [activityType.packageCreated],
      forms: [activityType.formSubmitted],
      booking: [activityType.bookingCreated],
      consultation_notes: [activityType.consultationNotesCreated],
    };

    return filterMap[filter]?.includes(type);
  });

  // Group filtered activities by date

  //console.log('filteredActivities', filteredActivities);
  const groupedActivities = filteredActivities?.reduce(
    (acc: any, item: any) => {
      const date = moment(item?.activity_date?.split('T')[0]).format(
        'MMMM D, YYYY'
      );

      //console.log('date', date);
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(item);
      //console.log('accc', acc);
      return acc;
    },
    {}
  );
  //console.log('groupedActivities', groupedActivities);

  const sortedGroupedActivities: any[] = Object.entries(groupedActivities).sort(
    ([dateA], [dateB]) =>
      moment(dateB, 'MMMM D, YYYY').valueOf() -
      moment(dateA, 'MMMM D, YYYY').valueOf()
  );

  // console.log('groupedActivities', groupedActivities);
  // console.log('filteredActivities', filteredActivities);
  const filterLabels: { [key: string]: string } = {
    all: 'Activity',
    notes: 'Note',
    emails: 'Email',
    invoice: 'Invoice',
    forms: 'Forms',
    packages: 'Package',
    transaction: 'Transaction',
    booking: 'Booking',
    consultation_notes: 'Consultation Notes',
  };

  //console.log('sortedGroupedActivities', sortedGroupedActivities);
  return (
    <CardWrapper>
      <Flex
        gap={'2'}
        alignItems={{ md: 'center' }}
        flexDirection={{ base: 'column', md: 'row' }}
        //justifyContent={'space-between'}
      >
        <HStack width={'full'}>
          <BsClock strokeWidth={0.3} size={17} color={'#000'} />
          <Heading fontSize={{ base: '18px', md: 'xl' }}>
            Client&apos;s Activities
          </Heading>
        </HStack>
        <ActivityFilter
          data={data}
          slp={user}
          setFilter={setFilter}
          filter={filter}
        />
      </Flex>

      {ClientActivitiesLoading ? (
        <Center w="100%" h="20rem">
          <AnimateLoader />
        </Center>
      ) : !filteredActivities?.length ? (
        <Center w="100%" mt="1rem">
          <Text>
            No {filterLabels[filter] || 'Activity'} for {data.first_name}
          </Text>
        </Center>
      ) : (
        <Stack gap={'1rem'} mt={'1rem'}>
          {sortedGroupedActivities?.map(([date, items]: any) => (
            <Box key={date}>
              <Heading fontSize="md" color="gray.600" mb="1rem">
                {date}
              </Heading>
              <Stack>
                {items.map((item: any, index: any) => {
                  const type = item.activity_type?.toLowerCase().trim();
                  const status = item?.status?.toLowerCase().trim() || '';
                  // <Box rounded={'md'} w={'full'} p={'5'}></Box>;
                  if (type === activityType.clientCreated) {
                    return <ClientCreated activities={item} key={index} />;
                  } else if (type === activityType.formSubmitted) {
                    return (
                      <FormSubmitted
                        activities={item}
                        key={index}
                        handleSwitchTab={handleSwitchTab}
                      />
                    );
                  } else if (type === activityType.consultationNotesCreated) {
                    return (
                      <ConsultationNotesCreated activities={item} key={index} />
                    );
                  } else if (type === activityType.transactionCreated) {
                    return (
                      <TransactionCreated
                        data={data}
                        activities={item}
                        key={index}
                      />
                    );
                  } else if (type === activityType.invoiceCreated) {
                    return (
                      <InvoiceCreated
                        key={index}
                        handleSwitchTab={handleSwitchTab}
                        slp={item.slp}
                        memo={item.memo}
                        date={item.created_at}
                        data={data}
                        total_hours={item.total_hours}
                        session_type={item.session_type}
                        activity={item}
                      />
                    );
                  } else if (type === activityType.profileUpdated) {
                    return (
                      <ProfileUpdated
                        //  data={data}
                        activities={item}
                        key={index}
                      />
                    );
                  } else if (
                    type === activityType.packageCreated &&
                    status !== 'voided'
                  ) {
                    return (
                      <PackageCreated
                        key={index}
                        handleSwitchTab={handleSwitchTab}
                        total={item.total}
                        product={item.product}
                        // date={item.created_at}
                        package_size={item.package_size}
                      />
                    );
                  } else if (type === activityType.sessionUpdated) {
                    return (
                      <SessionUpdated
                        slp={item.slp}
                        key={index}
                        // date={item.created_at}
                        note={item.note}
                      />
                    );
                  } else if (type === activityType.linkedPackages) {
                    return (
                      <Box key={item.id}>
                        <LinkedPackages item={item} />
                      </Box>
                    );
                  } else if (type === activityType.linkedTransactions) {
                    return (
                      <Box key={item.id}>
                        <LinkedTransaction item={item} />
                      </Box>
                    );
                  } else if (type === activityType.packageRefunded) {
                    return <RefundedCreated key={index} data={item} />;
                  } else if (['email_sent', 'email_received'].includes(type)) {
                    return <EmailCreated key={index} data={item} />;
                  } else if (
                    [
                      ActivityType.NOTE_CREATED,
                      ActivityType.NOTE_MODIFIED,
                    ].includes(type)
                  ) {
                    return (
                      <NoteCreated
                        key={index}
                        data={item}
                        clientId={data?.id}
                      />
                    );
                  } else if (type === activityType.bookingCreated) {
                    return (
                      <BookingCreated
                        key={index}
                        data={item}
                        handleSwitchTab={handleSwitchTab}
                      />
                    );
                  }
                })}
              </Stack>
            </Box>
          ))}
        </Stack>
      )}
    </CardWrapper>
  );
}

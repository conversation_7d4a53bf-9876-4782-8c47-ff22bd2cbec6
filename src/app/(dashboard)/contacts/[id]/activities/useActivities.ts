/* eslint-disable react-hooks/exhaustive-deps */

import { useGetClientActivitiesQuery } from '@/api/activities/get-all-activities';
import { activityType } from '@/constants/activity';
import { ActivityType } from '@/utils/enums';
import { useEffect, useState } from 'react';

export const useActivities = ({ data }: any) => {
  const [activities, setActivities] = useState<any>([]);
  const [completedActivities, setCompletedActivities] = useState<any>([]);
  const {
    data: ClientActivities,
    isLoading: ClientActivitiesLoading,
    isFetching,
  } = useGetClientActivitiesQuery(data?.id, {
    enabled: Boolean(data?.id),
  });

  const convertData = () => {
    const obj: any = {};
    data.invoices.forEach((invoice: any) => {
      const packageId = invoice.package_id;
      if (packageId) {
        if (obj[packageId]) {
          obj[packageId]?.push(invoice);
        } else {
          obj[packageId] = [invoice];
        }
      }
    });

    const invoicesOfCompletedPackage: any = [];
    // const invoicesOfInCompletedPackage: any = [];
    const linkedPackages = data.packages
      .filter((item: any) => Number(item.invoices?.length) > 0)
      .map((item: any) => ({ ...item, activity_type: 'linked_packages' }));
    const inCompletedPackages = data.packages.filter(
      (item: any) => Number(item.invoices?.length) === 0
    );

    Object.keys(obj).forEach((key) => {
      const packageData = data?.packages.find(
        (item: any) => Number(item.id) === Number(key)
      );
      if (packageData?.status === 'COMPLETED') {
        invoicesOfCompletedPackage.push(...obj[key]);
      }
    });
    const invoicesWithoutPackage = data.invoices.filter(
      (invoice: any) =>
        invoice.package_id === null && invoice.purchased_package_id === null
    );

    // console.log('Object is ', obj);
    // console.log(
    //   'invoicesOfInCompletedPackage is ',
    //   invoicesOfInCompletedPackage
    // );
    // console.log('invoicesOfCompletedPackage is ', invoicesOfCompletedPackage);
    // console.log('completedPackages is ', completedPackages);
    // console.log('inCompletedPackages is ', inCompletedPackages);
    // console.log('invoicesOfCompletedPackage is ', invoicesOfCompletedPackage);

    const invoicesActivities = invoicesWithoutPackage?.map((item: any) => {
      return {
        activity_type: activityType.invoiceCreated,
        slp: `${item?.slp?.first_name} ${item?.slp?.last_name}`,
        memo: item?.memo,
        created_at: item?.created_dt,
        invoice_items: item?.invoice_items || [],
        session_type: item?.session_type,
        total_hours: item?.total_hours,
        total_price: item?.total_price,
        activity_date: item?.invoice_date,
        status: item?.status,
        product: item?.product,
        invoice_date: item?.invoice_date,
        id: item?.id,
      };
    });

    //console.log('invoicesActivities is ', invoicesActivities);

    const packagesActivities = inCompletedPackages?.map((item: any) => {
      return {
        activity_type: activityType.packageCreated,
        balance: item?.balance,
        package_size: item?.package_size,
        product: item?.product,
        created_at: item?.created_at,
        total: item.total,
        status: item.status,
      };
    });

    const refundActivities =
      ClientActivities?.filter(
        (item: any) => item.activity_type === activityType.packageRefunded
      ).map((item: any) => {
        return {
          ...item,
          created_at: item.details.refund_date,
        };
      }) || [];

    // const refundActivities = data?.refunds?.map((item: any) => {
    //   return {
    //     activity_type: activityType.packageRefunded,
    //     created_at: item?.created_at,
    //     refund_date: item?.refund_date,
    //     total: item.amount,
    //     status: item.status,
    //     note: item.note,
    //   };
    // });

    // console.log('completedPackages is ', completedPackages);

    const clientCreatedActivity = ClientActivities
      ? ClientActivities?.filter(
          (activity: any) =>
            activity?.activity_type?.trim() === activityType?.clientCreated
        )
      : [];
    const profileUpdatedActivity = ClientActivities
      ? ClientActivities?.filter(
          (activity: any) =>
            activity?.activity_type?.trim() === activityType?.profileUpdated
        )
      : [];
    const formSubmittedActivity = ClientActivities
      ? ClientActivities?.filter(
          (activity: any) =>
            activity?.activity_type?.trim() === activityType?.formSubmitted
        )
      : [];
    const transactionCreatedActivityData = ClientActivities
      ? ClientActivities.filter(
          (activity: any) =>
            activity?.activity_type?.trim() === activityType?.transactionCreated
        )
      : [];

    const invoicesWithPurchasedPackage =
      data.invoices?.filter((invoice: any) => invoice.purchased_package_id) ||
      [];

    // 1. Filter out transaction activities that HAVE matching invoices
    // const transactionCreatedActivity = transactionCreatedActivityData.filter(
    //   (activity: any) => {
    //     const transactionId = activity.details?.transaction_id;
    //     return !invoicesWithPurchasedPackage.some(
    //       (invoice: any) => invoice.purchased_package_id === transactionId
    //     );
    //   }
    // );

    //split transaction created activities into two groups: one that has matching invoices and one that doesn't
    const transactionCreatedActivitiesWithInvoice =
      transactionCreatedActivityData.filter(
        (activity: any) => !!activity?.details?.invoice_id
      );
    const transactionCreatedActivitiesWithoutInvoice =
      transactionCreatedActivityData.filter(
        (activity: any) => !activity?.details?.invoice_id
      );
    const invoicesActivitiesWithTransactions = invoicesActivities?.map(
      (invoiceActivity: any) => {
        const matchingActivity = transactionCreatedActivitiesWithInvoice.filter(
          (activity: any) =>
            activity?.details?.invoice_id === invoiceActivity.id
        );
        return {
          ...invoiceActivity,
          transactionActivities: matchingActivity,
        };
      }
    );

    // 2. Create linkedTransactions ONLY from invoices that HAVE matching transaction activities
    const linkedTransactions = invoicesWithPurchasedPackage
      .map((invoice: any) => {
        const matchingActivity = transactionCreatedActivityData.find(
          (activity: any) =>
            activity.details?.transaction_id === invoice.purchased_package_id
        );

        return matchingActivity
          ? {
              ...matchingActivity,
              activity_type: activityType.linkedTransactions,
              details: {
                ...matchingActivity.details,
                invoices: invoicesWithPurchasedPackage.filter(
                  (inv: any) =>
                    inv.purchased_package_id ===
                    matchingActivity.details.transaction_id
                ),
              },
            }
          : null;
      })
      .filter(Boolean) // Remove null entries
      .filter(
        (
          item: any,
          index: any,
          self: any // Remove duplicates
        ) =>
          index ===
          self.findIndex(
            (t: any) => t.details.transaction_id === item.details.transaction_id
          )
      );

    // console.log('linkedTransactions is ', linkedTransactions);

    // console.log('clienet activites', ClientActivities);

    // console.log('formSubmittedActivity is ', formSubmittedActivity);
    const emailCreatedActivity = ClientActivities
      ? ClientActivities?.filter((activity: any) =>
          ['email_sent', 'email_received']?.includes(
            activity?.activity_type?.trim()
          )
        )
      : [];
    const noteCreatedActivity = ClientActivities
      ? ClientActivities?.filter((activity: any) =>
          [ActivityType.NOTE_CREATED, ActivityType.NOTE_MODIFIED]?.includes(
            activity?.activity_type?.trim()
          )
        )
      : [];
    const bookingActivities = ClientActivities
      ? ClientActivities?.filter(
          (activity: any) =>
            activity?.activity_type?.trim() === activityType?.bookingCreated
        )
      : [];
    const consultationNotesCreatedActivities = ClientActivities
      ? ClientActivities?.filter(
          (activity: any) =>
            activity?.activity_type?.trim() ===
            activityType?.consultationNotesCreated
        )
      : [];

    const allData = [
      ...invoicesActivitiesWithTransactions,
      ...formSubmittedActivity,
      ...linkedPackages,
      ...bookingActivities,
      ...transactionCreatedActivitiesWithoutInvoice,
      ...consultationNotesCreatedActivities,
      ...clientCreatedActivity,
      ...linkedTransactions,
      ...refundActivities,
      ...packagesActivities,
      ...emailCreatedActivity,
      ...noteCreatedActivity,
      ...profileUpdatedActivity,
    ].sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    const formattedData = allData
      ?.filter((item) => item.status?.toLowerCase() !== 'void')
      ?.filter((a: any) => a?.memo !== 'VOIDED');

    setActivities(formattedData);
    setCompletedActivities(linkedPackages);
  };

  useEffect(() => {
    if (ClientActivitiesLoading || isFetching) return;
    convertData();
  }, [ClientActivitiesLoading, isFetching]);

  return {
    activities,
    completedActivities,
    ClientActivitiesLoading,
  };
};

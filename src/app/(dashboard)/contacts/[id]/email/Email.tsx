'use client';

import { extractJsonFromString } from '@/app/(dashboard)/inbox/view-all';
import MailIcon from '@/assets/email.svg';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import EmptyState from '@/components/elements/EmptyState';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { useEmail } from '@/hooks/receptionist/contacts/useEmail';
import {
  Center,
  Flex,
  GridItem,
  HStack,
  SimpleGrid,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import moment from 'moment';
import { useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { IoIosRefresh } from 'react-icons/io';
import { IoSend } from 'react-icons/io5';
import { RiArrowLeftDoubleFill, RiArrowRightDoubleFill } from 'react-icons/ri';
import MailModal from './MailModal';
import { ViewEmail } from './ViewEmail';

export function Email({ id, form }: { id?: string; form?: any }) {
  const hook = useEmail({ id, form });
  const {
    GMisLoading,
    Client,
    data,
    open,
    isLoading,
    errors,
    setSearchResult,
    searchResult,
    touched,
    searchedClientId,
    replaceTags,
    values,
    handleCopyFormURL,
    showSearchedClient,
    templateHook,
    ClientLoading,
    handleSearchSelect,
    formatEmailAddress,
    handleOpen,
    onClose,
    handleSubmit,
    handleChange,
    setFieldValue,
    refetch,
    isFetching,
    dbDisclosure,
    dbIsLoading,
    handleDeleteEmail,
    setDeleteId,
    isPending,
  } = hook;
  const modalDisclosure = useDisclosure();
  const [selected, setSelected] = useState<any>(null);

  const transformData =
    data?.messages?.map((item: any) => ({
      ...item,
      threadMessages: extractJsonFromString(item?.threadMessages),
    })) || [];

  if (GMisLoading && !form) {
    return (
      <Center h={'15rem'}>
        <AnimateLoader />
      </Center>
    );
  }

  // console.log('form', form);

  return (
    <Stack gap={'4'}>
      {!form ? (
        <>
          <Flex
            flexDir={{ base: 'column', md: 'row' }}
            justifyContent={'space-between'}
            alignItems={{ base: 'flex-start', md: 'flex-end}' }}
            pb={'4'}
          >
            <Stack gap={'4'}>
              {/* <BreadcrumbRoot>
                <Link href={`/contacts/${id}`}>
                  {`${Client?.first_name || ''} ${Client?.last_name || ''}`}
                </Link>
                <BreadcrumbCurrentLink>Email</BreadcrumbCurrentLink>
              </BreadcrumbRoot> */}

              {/* <Heading fontWeight={500} fontSize={{ base: 'xs', md: '1.5rem' }}>
                {`${Client?.first_name || ''} ${Client?.last_name || ''} Emails thread`}
              </Heading> */}
            </Stack>
            <HStack gap={'4'}>
              <Button
                onClick={refetch}
                variant={'ghost'}
                loading={isFetching || isPending}
                loadingText={'Fetching new mail...'}
              >
                <IoIosRefresh />
              </Button>
              <Button
                bg={'primary.500'}
                onClick={() => handleOpen(null)}
                _hover={{ bg: 'gray.50' }}
              >
                Send Email
              </Button>
            </HStack>
          </Flex>

          <Stack gap={'6'}>
            {transformData?.length ? (
              <Stack gap={'6'}>
                {transformData?.map((message: any) => (
                  <SimpleGrid
                    key={message.id}
                    gap={{ base: '1', lg: '6' }}
                    columns={{ base: 1, md: 3, lg: 5 }}
                    py={'0.5'}
                    _hover={{ bg: 'gray.50', rounded: '12px' }}
                    // onClick={() => {
                    //   setSelected(message);
                    //   modalDisclosure.onOpen();
                    // }}
                  >
                    <GridItem
                      colSpan={1}
                      onClick={() => {
                        setSelected(message);
                        modalDisclosure.onOpen();
                      }}
                      cursor={'pointer'}
                    >
                      <HStack>
                        {message?.labelIds?.includes('SENT') ? (
                          <RiArrowLeftDoubleFill />
                        ) : (
                          <RiArrowRightDoubleFill />
                        )}
                        <Text lineClamp={1}>
                          {message?.labelIds?.includes('SENT')
                            ? formatEmailAddress(message.to)
                            : formatEmailAddress(message.from)}
                        </Text>
                      </HStack>
                    </GridItem>
                    <GridItem colSpan={{ base: 1, md: 2, lg: 3 }}>
                      <Text lineClamp={1}>{`${message.subject}`}</Text>
                    </GridItem>
                    <GridItem colSpan={1}>
                      <HStack justifyContent={'space-between'}>
                        <Text textAlign={{ base: 'left', lg: 'right' }}>
                          {moment(message.date).calendar(null, {
                            sameDay: '[Today at] h:mm A',
                            lastDay: '[Yesterday at] h:mm A',
                            nextDay: '[Tomorrow at] h:mm A',
                            lastWeek: 'dddd [at] h:mm A',
                            sameElse: 'MM/DD/YYYY [at] h:mm A',
                          })}
                        </Text>

                        <MenuRoot positioning={{ placement: 'bottom' }}>
                          <MenuTrigger cursor={'pointer'}>
                            <BsThreeDotsVertical />
                          </MenuTrigger>
                          <MenuContent cursor={'pointer'}>
                            <MenuItem
                              value="delete"
                              color={'red'}
                              bg={'transparent'}
                              cursor={'pointer'}
                              onClick={() => {
                                setDeleteId(message.id);
                                dbDisclosure.onOpen();
                              }}
                            >
                              Delete This Mail
                            </MenuItem>
                          </MenuContent>
                        </MenuRoot>
                      </HStack>
                    </GridItem>
                  </SimpleGrid>
                ))}
              </Stack>
            ) : (
              <EmptyState text="No Emails" img={MailIcon} />
            )}
          </Stack>
        </>
      ) : (
        <>
          <Flex
            alignItems={'center'}
            gap={'10px'}
            onClick={() => handleOpen(null)}
          >
            <IoSend size={16} />
            Send Email
          </Flex>
        </>
      )}
      <ViewEmail
        hook={hook}
        message={selected}
        modalDisclosure={modalDisclosure}
      />
      <ConsentDialog
        handleSubmit={handleDeleteEmail}
        open={dbDisclosure.open}
        onOpenChange={dbDisclosure.onClose}
        heading={'Do you want to delete this email from Soap?'}
        note="The original email will not be affected."
        isLoading={dbIsLoading}
        secondBtnText="Yes, Delete"
      />

      <CustomModal
        open={open}
        onOpenChange={onClose}
        closeOnInteractOutside={true}
        w={{ base: '40%', md: '40%' }}
        h={{ base: '100%', md: '80%' }}
      >
        <MailModal
          // handleSendEmail={handleSubmit}
          client={Client}
          isLoading={isLoading}
          values={values}
          errors={errors}
          replaceTags={replaceTags}
          searchResult={searchResult}
          handleCopyFormURL={handleCopyFormURL}
          setSearchResult={setSearchResult}
          form={form}
          templateHook={templateHook}
          touched={touched}
          showSearchedClient={showSearchedClient}
          handleSearchSelect={handleSearchSelect}
          searchedClientId={searchedClientId}
          clientLoading={ClientLoading}
          handleSubmit={handleSubmit}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
        />
      </CustomModal>
    </Stack>
  );
}

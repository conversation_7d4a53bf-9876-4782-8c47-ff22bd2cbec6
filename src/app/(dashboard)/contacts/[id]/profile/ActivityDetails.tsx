//import TextEditor from '@/components/Input/CustomEditor';
import { queryKey } from '@/constants/query-key';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { FullClient } from '@/shared/interface/clients';
import {
  // Badge,
  Box,
  Card,
  Flex,
  Grid,
  Heading,
  HStack,
  SimpleGrid,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { ReactNode, useEffect, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { FiCalendar, FiEdit3, FiUser, FiUsers } from 'react-icons/fi';
import { IoCheckmark } from 'react-icons/io5';
import { LiaTimesSolid } from 'react-icons/lia';
import Activities from '../activities/Activities';
import LinkedClients from './LinkedClients';
import Referrals from './Referrals';
// import CustomModal from '@/components/elements/modal/custom-modal';
import TextEditorNew from '@/components/Input/NewTextEditor';
import { toaster } from '@/components/ui/toaster';
import { getPrimaryEmail } from '@/utils/helper';
import Link from 'next/link';
import {
  LuCopy,
  LuExternalLink,
  LuStickyNote,
  LuUserPlus,
} from 'react-icons/lu';
import { useNotableDateHook } from '../notableDate/_hooks/useNotableDateHook';
import AddNotableDate from '../notableDate/AddNotableDateModal';
import NotableDates from '../notableDate/NotableDates';
import LinkClient from './LinkClient';
import AddReferralModal from './ReferralModal';
// import { useGetUserProfile } from '@/hooks/admin/users/useGetProfile';

const ReadOnlyData = ({
  title,
  children,
  isRow = false,
}: {
  title: string;
  children: ReactNode;
  isRow?: boolean;
}) => {
  return (
    <Box width="100%" display={isRow ? 'flex' : ''}>
      <Text fontSize={'sm'} color={'gray.300'} fontWeight={'600'}>
        {title}
      </Text>
      {children}
    </Box>
  );
};

export const CardWrapper = ({ children }: { children: ReactNode }) => (
  <Card.Root
    rounded="lg"
    border="1px solid"
    borderColor="gray.50"
    px="6"
    py="7"
  >
    {children}
  </Card.Root>
);

const ActivityDetails = ({
  data,
  switchToTab,
  getClientHook,
}: {
  data: FullClient;
  switchToTab: any;
  getClientHook: any;
  setShowEditClient: any;
}) => {
  const [notes, setNotes] = useState<any>(data.notes || 'No Notes');
  const [emailString, setEmailString] = useState<any>();
  const [editing, setEditing] = useState(false);
  const queryClient = useQueryClient();
  // const user = useGetUserProfile();
  const primaryEmail = getPrimaryEmail(data?.client_emails);

  const { open } = useDisclosure();
  const notableDateHook = useNotableDateHook({ client: data, size: 1000 });

  useEffect(() => {
    const res = getPrimaryEmail(data?.client_emails);
    setEmailString(res);
  }, [data]);

  const handleEdit = () => {
    setEditing(true);
  };

  const handleClose = () => {
    setEditing(false);
    setNotes(data.notes || 'No Notes');
  };

  const updateStatus = async () => {
    await supabase
      .from(tableNames.clients)
      .update({ notes: notes })
      .eq('id', data.id);

    await queryClient.invalidateQueries({
      queryKey: [queryKey.client.getById, Number(data.id)],
    });
  };

  const handleSave = () => {
    updateStatus();
    setEditing(false);
  };

  const handleEmailClick = (x: string) => {
    navigator.clipboard.writeText(x);
    toaster.create({
      description: 'Email copied to clipboard.',
      type: 'success',
    });
  };

  const handleSwitchTab = (tab: string) => {
    switchToTab(tab);
  };

  return (
    <>
      <Grid
        templateColumns={{ lg: '1fr .65fr' }}
        gap={{ base: '5', '2xl': '2rem' }}
      >
        <Box
          //order={{ base: 2, lg: 1 }} // On mobile, move this section to the bottom
          width="100%"
          spaceY={'5'}
        >
          <CardWrapper>
            <HStack>
              <FiUser size={20} color={'#000'} />
              <Heading fontSize={{ base: '18px', md: 'xl' }}>
                Client Information
              </Heading>
            </HStack>
            <SimpleGrid mt={'7'} columns={{ md: 2 }} gapY={'5'} gapX={'10'}>
              <ReadOnlyData title="Display Name">
                <Text
                  fontSize={{ base: 'base', md: 'lg' }}
                  fontWeight={'600'}
                  textTransform={'capitalize'}
                >
                  {data?.display_name ||
                    `${data?.first_name} ${data?.last_name}`}
                </Text>
              </ReadOnlyData>

              {primaryEmail && (
                <ReadOnlyData title="Email">
                  <HStack gap={'3'} fontWeight={'500'}>
                    <Text wordBreak={'break-all'}>{primaryEmail}</Text>
                    <Box
                      onClick={() => handleEmailClick(emailString)}
                      cursor={'pointer'}
                    >
                      <LuCopy size={17} />
                    </Box>
                  </HStack>
                </ReadOnlyData>
              )}

              {/* <ReadOnlyData title="Stage">
                <Badge
                  colorPalette="green"
                  rounded={'full'}
                  px={'2'}
                  fontWeight={'700'}
                  h={'fit'}
                >
                  {data?.stage}
                </Badge>
              </ReadOnlyData> */}
              {data?.phone && (
                <ReadOnlyData title="Phone">
                  <Text fontSize={'md'} fontWeight={'500'}>
                    {data?.phone}
                  </Text>
                </ReadOnlyData>
              )}

              {data?.dob && (
                <ReadOnlyData title="Date of Birth">
                  <Text fontSize={'md'} fontWeight={'500'}>
                    {new Date(data.dob).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </Text>
                </ReadOnlyData>
              )}

              {data?.address && (
                <ReadOnlyData title="Address">
                  <Text fontSize={'md'} fontWeight={'500'}>
                    {data?.address}
                  </Text>
                </ReadOnlyData>
              )}
              {(data?.city?.name ||
                data?.province ||
                data?.state?.name ||
                data?.country?.name) && (
                <ReadOnlyData title="Location">
                  <Text fontWeight={'500'}>
                    {[
                      data?.city?.name,
                      data?.province &&
                      data?.province !== data?.state?.name &&
                      data?.province !== data?.country?.name &&
                      !(data?.province === 'Other' && data?.state?.name)
                        ? data?.province
                        : null,
                      data?.state?.name,
                      data?.country?.name,
                    ]
                      .filter(Boolean)
                      .join(', ')}
                  </Text>
                </ReadOnlyData>
              )}

              {data?.postal_code && (
                <ReadOnlyData title="Postal Code">
                  <Text fontSize={'md'} fontWeight={'500'}>
                    {data?.postal_code}
                  </Text>
                </ReadOnlyData>
              )}

              {!open && data?.active_slp?.id ? (
                <ReadOnlyData title="Service Provider">
                  <Link href={`/slp/${data?.active_slp?.id}`}>
                    <HStack
                      textTransform={'capitalize'}
                      fontWeight={'500'}
                      color={'primary.300'}
                    >
                      {data?.active_slp?.first_name || ''}{' '}
                      {data?.active_slp?.last_name || ''} <LuExternalLink />
                    </HStack>
                  </Link>
                </ReadOnlyData>
              ) : (
                ''
              )}
              {data?.tags && data?.tags.length > 0 && (
                <ReadOnlyData title="Groups">
                  <Text fontWeight={'500'}>
                    {data?.tags?.map((item) => item?.tag_name)?.join(', ') ||
                      ''}
                  </Text>
                </ReadOnlyData>
              )}
              {data?.referral_source &&
                data?.referral_source !== 'Unknown' &&
                data?.referral_source !== '' && (
                  <ReadOnlyData title="Referral Source">
                    <Text fontWeight={'500'}>{data?.referral_source}</Text>
                  </ReadOnlyData>
                )}
            </SimpleGrid>
          </CardWrapper>
          {/**Client Activities */}
          <Activities data={data} handleSwitchTab={handleSwitchTab} />
        </Box>
        {/* Notes */}
        <Box
          order={{ base: 1, lg: 2 }} // On mobile, move this section to the top
          display="flex"
          flexDirection="column"
          gap=".6rem"
          width="100%"
        >
          <CardWrapper>
            <Flex
              justifyContent="space-between"
              alignItems="center"
              mb="0.5rem"
            >
              <HStack>
                <LuStickyNote size={17} color={'#000'} />
                <Text fontSize="1rem" fontWeight={'600'}>
                  Notes
                </Text>
              </HStack>

              {editing ? (
                <Flex alignItems="center" gap=".5rem">
                  <IoCheckmark
                    cursor="pointer"
                    onClick={handleSave}
                    color="green"
                    fontSize="2rem"
                  />
                  <LiaTimesSolid
                    cursor="pointer"
                    onClick={handleClose}
                    fontSize="2rem"
                    color="red"
                  />
                </Flex>
              ) : (
                <Flex gap={2} alignItems="center" justifyContent={'flex-end'}>
                  <Box
                    width={'30px'}
                    height={'30px'}
                    pl={'10px'}
                    display={'flex'}
                    justifyContent={'center'}
                    alignItems={'center'}
                  >
                    <FiEdit3 cursor="pointer" onClick={handleEdit} size={17} />
                  </Box>
                </Flex>
              )}
            </Flex>

            <Box mt={'2'}>
              {editing ? (
                <TextEditorNew
                  initialContent={notes}
                  saveContent={(e: any) => setNotes(e)}
                />
              ) : (
                <Text
                  whiteSpace="pre-wrap"
                  fontSize={'md'}
                  //fontWeight={'500'}
                  color={'gray.500'}
                  letterSpacing={'0.5px'}
                  className="noteBox"
                  dangerouslySetInnerHTML={{ __html: notes || 'No notes yet.' }}
                />
              )}
            </Box>
          </CardWrapper>

          <CardWrapper>
            <Flex
              alignItems="center"
              width={'100%'}
              justifyContent={'space-between'}
            >
              <HStack width={'100%'} mb={'3'}>
                <FiUsers size={17} color={'#000'} />
                <Text fontSize="1rem" fontWeight={'600'}>
                  Linked Clients
                </Text>
              </HStack>

              <LinkClient
                section={'profile'}
                data={data}
                getClientHook={getClientHook}
                variant={2}
              />
            </Flex>

            <LinkedClients data={data} />
          </CardWrapper>

          <CardWrapper>
            <Flex
              alignItems="center"
              width={'100%'}
              justifyContent={'space-between'}
            >
              <HStack>
                <LuUserPlus size={17} color={'#000'} />
                <Text fontSize="1rem" fontWeight={'600'}>
                  Referrals
                </Text>
              </HStack>
              <AddReferralModal
                variant={2}
                section={'profile'}
                data={data}
                getClientHook={getClientHook}
              />
            </Flex>
            <Referrals data={data} />
          </CardWrapper>

          <CardWrapper>
            <Flex
              alignItems="center"
              width={'100%'}
              justifyContent={'space-between'}
            >
              <HStack flexGrow={1}>
                <FiCalendar size={17} color={'#000'} />
                <Text whiteSpace={'nowrap'} fontSize="1rem" fontWeight={'600'}>
                  Notable Date
                </Text>
              </HStack>
              <FaPlus
                size={14}
                color={'black'}
                cursor={'pointer'}
                onClick={notableDateHook.onOpen}
              />
              <AddNotableDate notableDateHook={notableDateHook} />
            </Flex>
            <NotableDates notableDateHook={notableDateHook} />
          </CardWrapper>
        </Box>

        {/* Activities Section */}
      </Grid>

      {/* STILL WAITING FOR APPROVAL */}
      {/* <CustomModal
        modalWidth={{ base: '70%', md: '20%' }}
        modalHeight={'22rem'}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ChangeActiveSlp onClose={onClose} data={data} />
      </CustomModal> */}
    </>
  );
};

export default ActivityDetails;

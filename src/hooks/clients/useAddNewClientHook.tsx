import { useSlpIdViewQuery } from '@/api/users/slp-id-view';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { SLPUsersFilterState } from '@/store/filters/slp-user';
import { useFormik } from 'formik';
import { useSearchParams } from 'next/navigation';
import { FormEvent, useCallback, useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import * as Yup from 'yup';
import { useGetClientsHook } from '../../app/(dashboard)/contacts/useAllClients';
import { useCountryStateCityHook } from '../countryStateCity/useCountryStateCityHook';
import { createUser } from '@/utils/create-user';

export const useAddNewClientHook = ({
  onClose,
  id,
}: {
  onClose: any;
  id?: number;
}) => {
  const [filter] = useRecoilState(SLPUsersFilterState);
  const [loading, setLoading] = useState(false);
  const [displayNameError, setDisplayNameError] = useState(false);
  const [error, setError] = useState(false);
  const [isUserEditingDisplayName, setIsUserEditingDisplayName] = useState('');
  const getClientHook = useGetClientsHook();
  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');
  //console.log('organizationId', organizationId);

  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const { data: slp } = useGetUserByIdQuery(Number(id), {
    enabled: Boolean(id),
  });

  const { refetch } = useSlpIdViewQuery(id, slp?.organization_id, filter, {
    enabled: Boolean(slp?.id),
  });

  const initialValues: { [key: string]: any } = {
    first_name: '',
    last_name: '',
    middle_name: '',
    display_name: '',
    phone: '',
    lead_created: new Date().toISOString(),
    email: '',
    province: '',
    stage: '',
    country: null,
    state: null,
    city: null,
  };

  const validationSchema = Yup.object({
    first_name: Yup.string().trim().required('First name is required'),
    last_name: Yup.string().trim().required('Last name is required'),
    email: Yup.string()
      .email('Invalid email address')
      .required('Email is required'),
    province: Yup.string().required('Province is required').optional(),
    stage: Yup.string().required('Stage is required'),
  });

  const validateSelection = useCallback(
    (value: string) => {
      const [firstName, lastName] = value.trim().split(' ');
      if (firstName && lastName) setError(false);
      return !(firstName && lastName);
    },
    [setError]
  );

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    resetForm,
  } = useFormik({
    initialValues: initialValues,
    validationSchema, // Attach the validation schema
    onSubmit: async (values) => {
      try {
        setLoading(true);

        if (displayNameError) {
          setError(true);
          return;
        }

        const insert = {
          first_name: values.first_name,
          // middle_name: values.middle_name,
          last_name: values.last_name,
          lead_created: values?.lead_created,
          phone: values.phone || null,
          email: values.email?.toLowerCase() || null,
          display_name: values.display_name,
          province: values.province,
          stage: values.stage,
          active_slp: id || null,
          slp_notes: id ? 'Active' : null,
          country: values?.country,
          state: values?.state,
          organization_id: Number(organizationId) || Number(org?.id),
          city: values?.city,
          referral_source: values?.referral_source || null,
        };

        const data = await createUser(insert);
        //console.log('insert', insert);

        // create activites in client_activities table
        if (data) {
          const activitiesPayload = {
            client_id: data[0]?.id,
            activity_type: 'client_created',
            activity_date: new Date().toISOString(),
            details: {
              created_by: 'manual',
            },
            organization_id: data[0]?.organization_id,
          };
          const { error: activityUploadError } = await supabase
            .from(tableNames.client_activities)
            .insert(activitiesPayload);

          if (activityUploadError) {
            toaster.create({
              description: 'Something went wrong.',
              type: 'error',
            });
            throw activityUploadError;
          }
        }

        // if (values.email !== '') {
        //   const insertClientEmail = {
        //     client_id: data[0]?.id,
        //     email: values.email?.toLowerCase(),
        //     organization_id: data[0]?.organization_id,
        //   };
        //   const { error: emailError } = await supabase
        //     .from(tableNames.client_emails)
        //     .insert(insertClientEmail);

        //   if (emailError) {
        //     toaster.create({
        //       description: 'Something went wrong.',
        //       type: 'error',
        //     });
        //     throw emailError;
        //   }
        // }

        await getClientHook.refetchAllClient();
        refetch();
        onClose();
        toaster.create({
          description: 'Client Successfully Created',
          type: 'success',
        });
        resetForm();
      } catch (error) {
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
  });

  const { countryOptions, stateOptions, cityOptions } = useCountryStateCityHook(
    {
      countryCode: values?.country?.isoCode,
      stateCode: values?.state?.isoCode,
    }
  );

  useEffect(() => {
    if (isUserEditingDisplayName === 'false') {
      const firstName = values.first_name?.trim() || '';
      const lastName = values.last_name?.trim() || '';
      const autoGeneratedDisplayName = `${firstName} ${lastName}`.trim();

      if (autoGeneratedDisplayName) {
        setFieldValue('display_name', autoGeneratedDisplayName);
      } else {
        setFieldValue('display_name', '');
      }
      setDisplayNameError(validateSelection(autoGeneratedDisplayName));
    }
  }, [
    values.first_name,
    values.last_name,
    isUserEditingDisplayName,
    setDisplayNameError,
    validateSelection,
    setFieldValue,
  ]);

  const handleDisplayNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('true');
    setFieldValue('display_name', value);
    setDisplayNameError(validateSelection(value));
  };

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('false');
    setFieldValue('first_name', value);
  };

  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('false');
    setFieldValue('last_name', value);
  };

  const handleFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSubmit();
  };

  return {
    handleFormSubmit,
    loading,
    setError,
    error,
    setDisplayNameError,
    handleChange,
    errors,
    touched,
    setFieldValue,
    values,
    handleFirstNameChange,
    handleLastNameChange,
    handleDisplayNameChange,
    countryOptions,
    stateOptions,
    cityOptions,
  };
};

import { useDelete<PERSON><PERSON><PERSON><PERSON> } from '@/api/client_emails/delete-email';
import { useDeleteEmailApi } from '@/api/client_emails/delete-email-from-db';
import {
  useGetEmailsQuery,
  useProcessEmailMutation,
} from '@/api/client_emails/read-email';
import { useGetDBEmailsQuery } from '@/api/client_emails/read-email-from-db';
import { useSendEmailMutation } from '@/api/client_emails/send-email';
import { useGetClientByIdQuery } from '@/api/clients/get-client-by-id';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { toaster } from '@/components/ui/toaster';
import { useTemplateHook } from '@/hooks/admin/template/useTemplateHook';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { getPrimaryEmail } from '@/utils/helper';
import { useDisclosure } from '@chakra-ui/react';
import { useFormik } from 'formik';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';

const validationSchema = Yup.object({
  subject: Yup.string()
    .required('Subject is required')
    .min(1, 'Subject cannot be empty'),
  body: Yup.string()
    .required('Body is required')
    .min(1, 'Body cannot be empty'),
  to: Yup.string()
    .required('Recipient email is required')
    .email('Invalid email address'),
});

export const useEmail = ({ id, form }: { id: any; form?: any }) => {
  const dbDisclosure = useDisclosure();
  const path = usePathname();
  const slp_id = path.split('/')[2];

  const { UserFromQuery } = useSupabaseSession() as any;

  const {
    data: UsersData,
    // isFetching,
    // isLoading: dIsLoading,
    // refetch,
  } = useGetUserByIdQuery(Number(slp_id), {
    enabled: Boolean(slp_id),
  });

  // console.log('UsersData', UsersData);
  const templateHook = useTemplateHook({
    slp: UserFromQuery,
  });

  // console.log('UserFromQuery', UserFromQuery);

  // console.log('template', templateHook);
  // console.log('form', form);
  const [paginate, setPaginate] = useState({
    page: 1,
    pageSize: 20,
  });

  // const { data:SlpData } = useGetUserByIdQuery(Number(userId), {
  //   enabled: Boolean(userId),
  // });

  const organizationSlug =
    UsersData?.organization?.slug || UserFromQuery?.organization?.slug;

  const [searchedClientId, setSearchedClientId] = useState<number | null>(null);
  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [showSearchedClient, setShowSearchedClient] = useState(true);

  const [deleteId, setDeleteId] = useState('');
  const { mutateAsync, isLoading } = useSendEmailMutation();
  const { mutateAsync: deleteMutateAsync, isLoading: DisLoading } =
    useDeleteDraftApi();
  const { mutateAsync: dbMutateAsync, isLoading: dbIsLoading } =
    useDeleteEmailApi();
  const { data: Client, isLoading: ClientLoading } = useGetClientByIdQuery(
    searchedClientId ?? Number(id), // Use searchedClientId if available, otherwise fallback to id
    {
      enabled: Boolean(searchedClientId ?? id),
    }
  ) as any;

  const email = getPrimaryEmail(
    Client?.client_emails || Client?.clients?.client_emails
  );

  const {
    data: dbData,
    isLoading: GMisLoading,
    isFetching: GMisFetching,
    refetch: dbRefetch,
  } = useGetDBEmailsQuery(
    {
      query: email || '',
      page: paginate.page,
      pageSize: paginate.pageSize,
    },
    {
      enabled: Boolean(id ? email : true),
    }
  ) as any;

  const { mutate, isLoading: isPending } = useProcessEmailMutation();

  const { data, refetch, isFetching } = useGetEmailsQuery(
    {
      query: email || '',
      page: paginate.page,
      pageSize: paginate.pageSize,
    },
    {
      enabled: Boolean(id ? email : true),
      onSuccess: (res: any) => {
        if (res?.data?.uniqueMessageIds?.length > 0) {
          mutate({
            userEmail: res?.data?.userEmail,
            uniqueMessageIds: res?.data?.uniqueMessageIds,
            query: res?.data?.query,
            tokens: res?.data?.tokens,
          });
        }
      },
    }
  ) as any;

  const { open, onOpen, onClose } = useDisclosure();
  const [threadData, setThreadData] = useState<any>(null);

  const handleSearchSelect = (client: any) => {
    setSearchedClientId(client?.id);
    setShowSearchedClient(true);
  };

  const handleCopyFormURL = async () => {
    if (typeof window !== 'undefined') {
      try {
        await navigator.clipboard.writeText(
          `${window?.location.origin}/form/${organizationSlug}/${form?.slug}`
        );

        toaster.create({
          description: 'Copied Successfully',
          type: 'success',
        });
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    }
  };

  // 1. Update your replaceTags function:
  // Function to replace both {{link}} and raw URLs with proper anchor tags
  const replaceTags = (content: string, cb: (arg0: string) => void) => {
    const data = {
      first_name: Client?.first_name,
      invoice_date: '',
      link: `${window?.location.origin}/form/${organizationSlug}/${form?.slug}`,
      organization: form?.organization_name,
    };

    // Step 1: Replace all standard template tags
    let result = templateHook?.replaceTags(content, data);

    // Step 2: Replace {{link}} placeholders with anchor tags
    if (result.includes('{{link}}')) {
      result = result.replace(
        /{{link}}/g,
        `<a href="${data.link}" target="_blank" rel="noopener noreferrer">Open Private Workspace</a>`
      );
    }

    // Step 3: Replace any raw URL patterns that match the link
    const linkUrl = data.link;
    const escapedUrl = linkUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape special regex characters
    const urlPattern = new RegExp(escapedUrl, 'g');

    result = result.replace(
      urlPattern,
      `<a href="${linkUrl}" target="_blank" rel="noopener noreferrer">Open Private Workspace</a>`
    );

    cb(result);
  };

  const formatEmailAddress = (email: string | undefined): string => {
    if (!email) return '';

    if (email.includes(',')) {
      return email
        .split(',')
        .map((e) => {
          const match = e.trim().match(/<([^>]+)>/) || [null, e.trim()];
          return match[1];
        })
        .join(', ');
    }

    const match = email.match(/<([^>]+)>/) || [null, email];
    return match[1];
  };

  function formatSubject(subject: string) {
    if (subject?.trim().toLowerCase().startsWith('re:')) {
      return subject;
    }
    return `Re: ${subject}`;
  }

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    const today = new Date();

    if (date.toDateString() === today.toDateString()) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
      });
    }

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  function downloadAttachment(attachment: any) {
    if (!attachment || !attachment.filename || !attachment.data) {
      console.error('Invalid attachment data');
      return;
    }

    try {
      const cleanBase64 = attachment.data
        .replace(/\s/g, '')
        .replace(/-/g, '+')
        .replace(/_/g, '/');
      const decodedData = atob(cleanBase64);

      const blob = new Blob(
        [Uint8Array.from(decodedData, (c) => c.charCodeAt(0))],
        {
          type: attachment.mimeType || 'application/octet-stream',
        }
      );

      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = attachment.filename;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toaster.create({
        description: 'Attachment downloaded successfully',
        type: 'success',
      });
    } catch (error: any) {
      toaster.create({
        description: `Error downloading attachment: ${error.message}`,
        type: 'error',
      });
    }
  }

  const handleOpen = (x: any) => {
    setThreadData(x);
    setSearchedClientId(null);
    if (form) {
      setShowSearchedClient(false);
      //console.log('this is form');
    }
    onOpen();
  };

  // console.log('form', form);

  const initialValues = {
    to: email || '',
    subject: form
      ? form?.organization_name + ' - ' + form?.title
      : threadData
        ? formatSubject(threadData?.subject)
        : '',
    body: form
      ? `Please complete the form ${form?.title}  here : ${window?.location.origin}/form/${organizationSlug}/${form?.slug}`
      : '',
    schedule: false,
    bcc: false,
    scheduledTime: '',
    isDraft: '',
    attachment: [],
  };

  // console.log('initialValues', initialValues);

  const { values, handleSubmit, errors, touched, handleChange, setFieldValue } =
    useFormik({
      initialValues: initialValues,
      validationSchema,
      enableReinitialize: true,
      onSubmit: async (values) => {
        const formData = new FormData();
        formData.append('to', values.to);
        formData.append(
          'subject',
          values.subject ? values.subject : threadData?.subject
        );
        formData.append('htmlBody', values.body);
        formData.append(
          'scheduleAt',
          values.schedule ? values.scheduledTime : ''
        );
        formData.append('threadId', threadData?.threadId);
        formData.append('isDraft', values.isDraft || '');

        values.attachment.forEach((file, index) => {
          formData.append(`attachment-${index}`, file);
        });

        await mutateAsync(formData);
        onClose();
      },
    });

  // console.log('values', values);

  const handleSendDraftEmail = async (draftId: string) => {
    const formData = new FormData();
    formData.append('draftId', draftId);
    formData.append('sendDraft', 'true');
    await mutateAsync(formData);
  };

  const handleDeleteDraft = async (draftId: string) => {
    await deleteMutateAsync(draftId);
  };
  const handleDeleteEmail = async () => {
    await dbMutateAsync(deleteId);
    dbDisclosure.onClose();
    setDeleteId('');
  };

  useEffect(() => {
    if (data?.error) {
      toaster.create({
        description: `${data.message} - ${data?.error}`,
        type: 'error',
      });
    }
    if (!data?.error) {
      dbRefetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return {
    GMisLoading,
    data: dbData,
    Client,
    open,
    isLoading,
    DisLoading,
    threadData,
    values,
    errors,
    handleSearchSelect,
    ClientLoading,
    templateHook,
    touched,
    formatEmailAddress,
    formatDate,
    downloadAttachment,
    setSearchResult,
    searchResult,
    onClose,
    handleOpen,
    handleSendDraftEmail,
    showSearchedClient,
    searchedClientId,
    handleDeleteDraft,
    handleCopyFormURL,
    replaceTags,
    handleSubmit,
    handleChange,
    setFieldValue,
    setPaginate,
    paginate,
    refetch,
    isFetching,
    dbRefetch,
    GMisFetching,
    handleDeleteEmail,
    dbIsLoading,
    dbDisclosure,
    setDeleteId,
    isPending,
  };
};

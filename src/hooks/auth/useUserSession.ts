import { useGetUserByEmailQuery } from '@/api/users/get-user-by-email';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { type Session, type User } from '@supabase/supabase-js';
import { useCallback, useEffect, useState } from 'react';

export const useSupabaseSession = () => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const supabase = createClientComponentClient();
  const {
    data: UserFromQuery,
    isLoading: UserFromQueryLoading,
    refetch: RefetchUserQuery,
  } = useGetUserByEmailQuery(String(user?.email), {
    enabled: Bo<PERSON>an(user?.email),
  });

  // console.log('user', user);
  // console.log('UserFromQuery', UserFromQuery);

  // Fetch the session when the hook is used
  useEffect(() => {
    const fetchSession = async () => {
      const { data, error } = await supabase.auth.getSession();

      // console.log('data----4', data);
      if (error) {
        console.error('Error fetching session:', error.message);
      } else {
        setSession(data.session);
        setUser(data.session?.user || null);
      }
    };

    fetchSession();

    // Listen for auth changes and update session
    const { data: subscription } = supabase.auth.onAuthStateChange(() => {
      fetchSession();
    });

    return () => {
      subscription.subscription.unsubscribe();
    };
  }, [supabase]);

  // Update user profile or session data
  const updateUserProfile = useCallback(
    async (updates: Partial<User>) => {
      if (!session) {
        console.error('No session available to update user profile.');
        return;
      }

      const { error } = await supabase.auth.updateUser(updates);
      if (error) {
        console.error('Error updating user:', error.message);
        return;
      }

      // Refresh session and user data
      const { data: updatedSession } = await supabase.auth.getSession();
      setSession(updatedSession?.session || null);
      setUser(updatedSession?.session?.user || null);
    },
    [supabase, session]
  );

  return {
    session,
    user,
    updateUserProfile,
    UserFromQuery,
    UserFromQueryLoading,
    RefetchUserQuery,
  };
};
